@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page-container.data-v-134a5055 {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.header.data-v-134a5055 {
  padding: 40rpx 30rpx 30rpx;
  background-color: #fff;
}
.header .header-title.data-v-134a5055 {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
}
.content.data-v-134a5055 {
  padding: 30rpx;
  padding-bottom: 150rpx;
  /* 为底部tabbar留出空间 */
}
.service-list.data-v-134a5055 {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.service-card.data-v-134a5055 {
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.service-card.data-v-134a5055:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
}
.service-card .card-content.data-v-134a5055 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.service-card .service-text.data-v-134a5055 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.service-card .service-icon.data-v-134a5055 {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.service-card .service-icon .icon-image.data-v-134a5055 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 10rpx;
}
.service-card .service-icon .icon-placeholder.data-v-134a5055 {
  font-size: 50rpx;
}
/* 不同服务卡片的背景色 */
.repair-card.data-v-134a5055 {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
}
.install-card.data-v-134a5055 {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
}
.install-card .install-icon.data-v-134a5055 {
  color: #ff9800;
}
.clean-card.data-v-134a5055 {
  background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%);
}
.rescue-card.data-v-134a5055 {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
}

