<view class="page data-v-ba180efa"><view class="time data-v-ba180efa"><text class="data-v-ba180efa">支付剩余时间</text><u-count-down vue-id="88e8ce3a-1" time="{{15*60*1000}}" format="mm:ss" class="data-v-ba180efa" bind:__l="__l"></u-count-down></view><label class="price _span data-v-ba180efa"><label class="_span data-v-ba180efa">￥</label>{{allprice}}</label><view class="payCard data-v-ba180efa"><view class="left data-v-ba180efa"><image src="../static/svg/weixinfang.svg" mode="aspectFill" class="data-v-ba180efa"></image><text class="data-v-ba180efa">微信支付</text></view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="choose data-v-ba180efa" style="{{(currentIndex==0?'background:#2E80FE;border:2rpx solid #2E80FE':'')}}" bindtap="__e"><u-icon vue-id="88e8ce3a-2" name="checkbox-mark" color="#fff" size="16" class="data-v-ba180efa" bind:__l="__l"></u-icon></view></view><view class="footer data-v-ba180efa"><view data-event-opts="{{[['tap',[['confirmPay',['$event']]]]]}}" class="btn data-v-ba180efa" bindtap="__e">确认支付</view></view></view>