@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
view.data-v-4a8c9de7, scroll-view.data-v-4a8c9de7, swiper-item.data-v-4a8c9de7 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-switch.data-v-4a8c9de7 {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  position: relative;
  background-color: #fff;
  border-width: 1px;
  border-radius: 100px;
  transition: background-color 0.4s;
  border-color: rgba(0, 0, 0, 0.12);
  border-style: solid;
  justify-content: flex-end;
  align-items: center;
  overflow: hidden;
}
.u-switch__node.data-v-4a8c9de7 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
  background-color: #fff;
  border-radius: 100px;
  box-shadow: 1px 1px 1px 0 rgba(0, 0, 0, 0.25);
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  transition-duration: 0.4s;
  transition-timing-function: cubic-bezier(0.3, 1.05, 0.4, 1.05);
}
.u-switch__bg.data-v-4a8c9de7 {
  position: absolute;
  border-radius: 100px;
  background-color: #FFFFFF;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  transition-duration: 0.4s;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  transition-timing-function: ease;
}
.u-switch--disabled.data-v-4a8c9de7 {
  opacity: 0.6;
}

