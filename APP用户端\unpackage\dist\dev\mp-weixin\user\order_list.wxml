<view class="page data-v-1ee6bdd6"><view class="header data-v-1ee6bdd6"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleHeader',['$0'],[[['list','',index]]]]]]]}}" class="header_item data-v-1ee6bdd6" bindtap="__e"><view style="{{(currentIndex===item.value?'color:#2E80FE;':'')}}" class="data-v-1ee6bdd6">{{item.name}}</view><view class="blue data-v-1ee6bdd6" style="{{(currentIndex===item.value?'':'background-color:#fff;')}}"></view></view></block></view><block wx:if="{{$root.g0===0}}"><u-empty vue-id="21080291-1" mode="order" icon="http://cdn.uviewui.com/uview/empty/order.png" class="data-v-1ee6bdd6" bind:__l="__l"></u-empty></block><block wx:if="{{$root.g1===0}}"><view style="text-align:center;padding:20rpx;color:#999;" class="data-v-1ee6bdd6">{{'当前订单数量: '+$root.g2+''}}</view></block><view data-event-opts="{{[['tap',[['dingyue']]]]}}" class="main data-v-1ee6bdd6" bindtap="__e"><block wx:for="{{$root.l2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="data-v-1ee6bdd6"><block wx:if="{{item.$orig.payType>=-1}}"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="main_item data-v-1ee6bdd6" bindtap="__e"><view class="head data-v-1ee6bdd6"><view class="no data-v-1ee6bdd6">{{"单号："+item.$orig.orderCode}}</view><view class="type data-v-1ee6bdd6">{{(item.$orig.payType===-1?'已取消':pay_typeArr[item.m0])+''}}</view></view><view class="order-type-info data-v-1ee6bdd6"><text class="order-type-label data-v-1ee6bdd6">报价类型：</text><text class="order-type-value data-v-1ee6bdd6">{{item.$orig.type===0?'一口价模式':item.$orig.type===1?'报价模式':'未知类型'}}</text></view><view class="mid data-v-1ee6bdd6"><view class="lef data-v-1ee6bdd6"><image src="{{item.$orig.goodsCover}}" mode class="data-v-1ee6bdd6"></image><text class="data-v-1ee6bdd6">{{item.$orig.goodsName}}</text><block wx:if="{{item.$orig.type===5}}"><text style="color:#F60100;" class="data-v-1ee6bdd6">活动订单</text></block></view><block wx:if="{{item.m1}}"><view class="righ data-v-1ee6bdd6"><view class="data-v-1ee6bdd6">{{"￥"+item.$orig.payPrice}}</view><view class="data-v-1ee6bdd6">{{"x"+(item.$orig.num?item.$orig.num:1)}}</view></view></block></view><view class="bot data-v-1ee6bdd6"><text class="data-v-1ee6bdd6">{{item.$orig.createTime}}</text><block wx:if="{{item.m2}}"><view data-event-opts="{{[['tap',[['gozhifua',['/user/Cashier?id='+item.$orig.id+'&price='+item.$orig.payPrice+'&type='+item.$orig.payType+'&goodsId='+item.$orig.goodsId]]]]]}}" class="qzf data-v-1ee6bdd6" catchtap="__e">去支付</view></block><block wx:if="{{item.m3}}"><view data-event-opts="{{[['tap',[['goUrl',['/user/huodongCashier?id='+item.$orig.id+'&price='+item.$orig.payPrice+'&type='+item.$orig.payType+'&goodsId='+item.$orig.goodsId]]]]]}}" class="qzf data-v-1ee6bdd6" catchtap="__e">去支付</view></block><block wx:if="{{item.m4}}"><view data-event-opts="{{[['tap',[['huodongquxiaos',['$0'],[[['orderList','',index]]]]]]]}}" class="qzf data-v-1ee6bdd6" catchtap="__e">取消订单</view></block><block wx:if="{{item.m5}}"><view data-event-opts="{{[['tap',[['confirmorder',['$0'],[[['orderList','',index]]]]]]]}}" class="qrwc data-v-1ee6bdd6" catchtap="__e">确认完成</view></block><block wx:if="{{item.$orig.payType>=2&&item.$orig.payType<7&&item.$orig.refundStatus===0}}"><view data-event-opts="{{[['tap',[['applyT',['$0'],[[['orderList','',index]]]]]]]}}" class="qrwc data-v-1ee6bdd6" catchtap="__e">申请退款</view></block><block wx:if="{{item.$orig.payType>=1&&item.$orig.payType<7&&item.$orig.refundStatus!==0}}"><view data-event-opts="{{[['tap',[['viewRefundDetails',['$0'],[[['orderList','',index]]]]]]]}}" class="qrwc data-v-1ee6bdd6" catchtap="__e">查看退款详情</view></block><block wx:if="{{item.m6}}"><view data-event-opts="{{[['tap',[['huodongwanchengclick',['$0'],[[['orderList','',index]]]]]]]}}" class="qrwc data-v-1ee6bdd6" catchtap="__e">确认完成</view></block><block wx:if="{{item.m7}}"><view data-event-opts="{{[['tap',[['huodongclick']]]]}}" class="qrwc data-v-1ee6bdd6" style="color:#999999;background-color:#f0f0f0;" catchtap="__e">待上门</view></block><block wx:if="{{item.m8}}"><view data-event-opts="{{[['tap',[['gohuodongevaluate',['$0'],[[['orderList','',index]]]]]]]}}" class="qpl data-v-1ee6bdd6" catchtap="__e">去评价</view></block><block wx:if="{{item.m9}}"><view class="qpl data-v-1ee6bdd6">已评价</view></block><block wx:if="{{item.m10}}"><view data-event-opts="{{[['tap',[['goevaluate',['$0'],[[['orderList','',index]]]]]]]}}" class="qpl data-v-1ee6bdd6" catchtap="__e">去评价</view></block><block wx:if="{{item.m11}}"><view class="qpl data-v-1ee6bdd6">已评价</view></block></view><block wx:if="{{item.g3}}"><view class="sub_orders data-v-1ee6bdd6"><view class="sub_title data-v-1ee6bdd6">差价申请记录</view><block wx:for="{{item.l0}}" wx:for-item="diffItem" wx:for-index="diffIndex" wx:key="id"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="sub_item data-v-1ee6bdd6" catchtap="__e"><view class="sub_head data-v-1ee6bdd6"><view class="sub_no data-v-1ee6bdd6">{{"差价单号："+diffItem.$orig.diffCode}}</view><view class="sub_status data-v-1ee6bdd6">{{diffItem.m12}}</view></view><view class="sub_content data-v-1ee6bdd6"><view class="sub_info data-v-1ee6bdd6"><view class="sub_amount data-v-1ee6bdd6">{{"差价金额：￥"+diffItem.$orig.diffAmount}}</view><view class="sub_reason data-v-1ee6bdd6">{{"原因："+diffItem.$orig.reasonDetail}}</view><view class="sub_time data-v-1ee6bdd6">{{"申请时间："+diffItem.$orig.createdTime}}</view><block wx:if="{{diffItem.$orig.partsImgs}}"><view class="sub_parts data-v-1ee6bdd6"><text class="sub_parts_label data-v-1ee6bdd6">配件图片：</text><image class="sub_parts_img data-v-1ee6bdd6" style="width:120rpx;height:120rpx;border-radius:8rpx;margin-left:10rpx;" src="{{diffItem.$orig.partsImgs}}" mode="aspectFit" data-event-opts="{{[['tap',[['previewImage',['$0'],[[['orderList','',index],['orderDiffPrices','id',diffItem.$orig.id,'partsImgs']]]]]]]}}" bindtap="__e"></image></view></block></view><view class="sub_actions data-v-1ee6bdd6"><block wx:if="{{diffItem.$orig.status===0}}"><view data-event-opts="{{[['tap',[['confirmDiffPrice',['$0'],[[['orderList','',index],['orderDiffPrices','id',diffItem.$orig.id]]]]]]]}}" class="sub_qzf data-v-1ee6bdd6" catchtap="__e">确认差价</view></block><block wx:if="{{diffItem.$orig.status===0}}"><view data-event-opts="{{[['tap',[['rejectDiffPrice',['$0'],[[['orderList','',index],['orderDiffPrices','id',diffItem.$orig.id]]]]]]]}}" class="sub_qzf sub_reject data-v-1ee6bdd6" catchtap="__e">拒绝差价</view></block><block wx:if="{{diffItem.$orig.status===1}}"><view data-event-opts="{{[['tap',[['payDiffPrice',['$0'],[[['orderList','',index],['orderDiffPrices','id',diffItem.$orig.id]]]]]]]}}" class="sub_qzf data-v-1ee6bdd6" catchtap="__e">去支付</view></block></view></view></view></block></view></block></view></block><block wx:else><view data-event-opts="{{[['tap',[['goChoose',['$0'],[[['orderList','',index]]]]]]]}}" class="main_item_already data-v-1ee6bdd6" bindtap="__e"><view class="title data-v-1ee6bdd6" style="font-size:32rpx;font-weight:500;">{{''+(item.g4===0?'等待师傅报价':'等待您选择师傅')+''}}</view><block wx:if="{{item.g5>0}}"><view class="ok data-v-1ee6bdd6">已有师傅报价</view></block><view class="no data-v-1ee6bdd6">{{"单号："+item.$orig.orderCode}}</view><view class="mid data-v-1ee6bdd6"><view class="lef data-v-1ee6bdd6"><image src="{{item.$orig.goodsCover}}" mode class="data-v-1ee6bdd6"></image><text class="data-v-1ee6bdd6">{{item.$orig.goodsName}}</text></view><text class="data-v-1ee6bdd6">{{"数量:"+item.$orig.num}}</text></view><view class="bot data-v-1ee6bdd6"><text class="data-v-1ee6bdd6">{{item.$orig.createTime}}</text></view><view class="shifu data-v-1ee6bdd6"><scroll-view scroll-x="true" class="data-v-1ee6bdd6"><block wx:for="{{item.l1}}" wx:for-item="shfItem" wx:for-index="shfIndex" wx:key="shfIndex"><view class="shifu_item data-v-1ee6bdd6"><image src="{{shfItem.$orig.selfImg?shfItem.$orig.selfImg:'/static/mine/default_user.png'}}" mode="aspectFit" class="data-v-1ee6bdd6"></image><text class="data-v-1ee6bdd6">{{"￥"+shfItem.g6}}</text></view></block></scroll-view></view><block wx:if="{{item.g7>0}}"><view style="display:flex;justify-content:center;align-items:center;margin-top:20rpx;" class="data-v-1ee6bdd6"><view data-event-opts="{{[['tap',[['cancelorder',['$0'],[[['orderList','',index]]]]]]]}}" class="qxdd data-v-1ee6bdd6" catchtap="__e">取消订单</view><view class="tips data-v-1ee6bdd6" style="margin-left:20%;" vif="item.quotedPriceVos.length > 0">{{''+item.g8+'位师傅已报价'}}</view><view class="qxdd data-v-1ee6bdd6" style="margin-left:20rpx;">选择师傅</view></view></block><block wx:if="{{item.m13}}"><view data-event-opts="{{[['tap',[['cancelorder',['$0'],[[['orderList','',index]]]]]]]}}" class="qxdd data-v-1ee6bdd6" catchtap="__e">取消订单</view></block></view></block></view></block></view><u-modal vue-id="21080291-2" show="{{showCancel}}" title="取消订单" content="确认要取消该订单吗" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e1']]],['^confirm',[['confirmCancel']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-1ee6bdd6" bind:__l="__l"></u-modal><u-modal vue-id="21080291-3" show="{{showConfirm}}" title="完成订单" content="确认要完成该订单吗" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e2']]],['^confirm',[['confirmconfirm']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-1ee6bdd6" bind:__l="__l"></u-modal><u-modal vue-id="21080291-4" show="{{showPaymentModal}}" title="提示" showCancelButton="{{true}}" confirm-text="去支付" data-event-opts="{{[['^cancel',[['e3']]],['^confirm',[['confirmPayment']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-1ee6bdd6" bind:__l="__l" vue-slots="{{['default']}}"><view class="modal-content-red data-v-1ee6bdd6">{{''+paymentRemind+''}}</view></u-modal><u-modal vue-id="21080291-5" show="{{showRefundModal}}" title="提示" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['e4']]],['^confirm',[['confirmRefund']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-1ee6bdd6" bind:__l="__l" vue-slots="{{['default']}}"><view class="modal-content-red data-v-1ee6bdd6">{{''+reminddata+''}}</view></u-modal><u-modal vue-id="21080291-6" show="{{showRefundDetailsModal}}" title="退款详情" confirm-text="关闭" data-event-opts="{{[['^cancel',[['e5']]],['^confirm',[['e6']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-1ee6bdd6" bind:__l="__l" vue-slots="{{['default']}}"><view class="modal-content-details data-v-1ee6bdd6"><view class="detail-item data-v-1ee6bdd6"><text class="label data-v-1ee6bdd6">退款单号:</text><text class="value data-v-1ee6bdd6">{{refundDetails.orderCode}}</text></view><view class="detail-item data-v-1ee6bdd6"><text class="label data-v-1ee6bdd6">申请时间:</text><text class="value data-v-1ee6bdd6">{{refundDetails.createTime}}</text></view><view class="detail-item data-v-1ee6bdd6"><text class="label data-v-1ee6bdd6">状态:</text><text class="value data-v-1ee6bdd6">{{$root.m14}}</text></view><block wx:if="{{refundDetails.refundText}}"><view class="detail-item data-v-1ee6bdd6"><text class="label data-v-1ee6bdd6">驳回备注:</text><text class="value data-v-1ee6bdd6">{{refundDetails.refundText}}</text></view></block><block wx:if="{{refundDetails.refundTime}}"><view class="detail-item data-v-1ee6bdd6"><text class="label data-v-1ee6bdd6">审核时间:</text><text class="value data-v-1ee6bdd6">{{refundDetails.refundTime}}</text></view></block><block wx:if="{{refundDetails.outRefundNo}}"><view class="detail-item data-v-1ee6bdd6"><text class="label data-v-1ee6bdd6">退款回执单号:</text><text class="value data-v-1ee6bdd6">{{refundDetails.outRefundNo}}</text></view></block></view></u-modal><block wx:if="{{$root.g9>=10}}"><view style="display:flex;justify-content:center;" class="data-v-1ee6bdd6"><u-loadmore vue-id="21080291-7" status="{{status}}" class="data-v-1ee6bdd6" bind:__l="__l"></u-loadmore></view></block></view>