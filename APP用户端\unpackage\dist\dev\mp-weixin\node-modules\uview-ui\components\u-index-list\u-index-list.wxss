@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
view.data-v-aecf76f2, scroll-view.data-v-aecf76f2, swiper-item.data-v-aecf76f2 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-index-list__letter.data-v-aecf76f2 {
  position: fixed;
  right: 0;
  text-align: center;
  z-index: 3;
  padding: 0 6px;
}
.u-index-list__letter__item.data-v-aecf76f2 {
  width: 16px;
  height: 16px;
  border-radius: 100px;
  margin: 1px 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.u-index-list__letter__item--active.data-v-aecf76f2 {
  background-color: #3c9cff;
}
.u-index-list__letter__item__index.data-v-aecf76f2 {
  font-size: 12px;
  text-align: center;
  line-height: 12px;
}
.u-index-list__indicator.data-v-aecf76f2 {
  width: 50px;
  height: 50px;
  border-radius: 100px 100px 0 100px;
  text-align: center;
  color: #ffffff;
  background-color: #c9c9c9;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.u-index-list__indicator__text.data-v-aecf76f2 {
  font-size: 28px;
  line-height: 28px;
  font-weight: bold;
  color: #fff;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  text-align: center;
}

