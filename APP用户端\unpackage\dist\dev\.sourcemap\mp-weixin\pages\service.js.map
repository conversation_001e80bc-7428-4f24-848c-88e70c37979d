{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/service.vue?84ca", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/service.vue?a435", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/service.vue?679a", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/service.vue?954b", "uni-app:///pages/service.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/service.vue?841c", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/pages/service.vue?9976"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "showLargePromoPopup", "showActivityPopup", "showConfirmPopup", "showLoginPopup", "list1", "servicecity", "bannerList", "text1", "huodongdata", "showmsg", "district", "position", "baseList", "service", "couponList", "isLoading", "isRefreshing", "isServiceLoading", "isPopupShrunk", "scrollTimeout", "isPageLoaded", "onLoad", "scene", "console", "uni", "isLargePromoClosed", "onPullDownRefresh", "onShow", "autograph", "onShareAppMessage", "onPageScroll", "clearTimeout", "methods", "closeLargePromoPopup", "grabDeal", "closeActivityPopup", "showConfirm", "closeConfirmPopup", "closeLoginPopup", "goToLogin", "url", "fail", "title", "icon", "confirmToHuodongParity", "duration", "<PERSON><PERSON><PERSON>u", "couponId", "coupon", "haveGet", "debounced<PERSON><PERSON>ling<PERSON><PERSON>", "getBottom", "initData", "Promise", "getcount", "gethuodongconfig", "<PERSON><PERSON><PERSON>", "getSearchList", "res", "getCoupon", "userId", "goUrl", "copyMethod", "res4", "getNowPosition", "locationManager", "forceUpdate", "silent", "locationData", "key", "val", "computed", "primaryColor", "subColor", "configInfo", "commonOptions", "userInfo", "userPageType", "mineInfo", "serviceCateData"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAs1B,CAAgB,s2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC4L12B;AAKA;AAEA;AAGA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cACAC;cACA;gBACA;gBACAC;gBACAD;cACA;gBACAA;cACA;cACA;cACAE;cACA;gBACA;cACA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACAH;cAAA,KACA;gBAAA;gBAAA;cAAA;cACAC;cAAA;YAAA;cAGA;cAAA;cAAA;cAAA,OAEA;YAAA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEAD;YAAA;cAAA;cAEA;cACAC;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAG;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cAAA,MACAA;gBAAA;gBAAA;cAAA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACAC;EACAC;IAAA;IACA;MACA;IACA;IACA;MACAC;IACA;IACA;MACA;IACA;EACA;EACAC;IAKAC;MACA;MACA;MACAT;IACA;IACAU;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACAf;QACAgB;QACAC;UACAlB;UACAC;YACAkB;YACAC;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;MACA;MACA;MACApB;MACA;QACA;MACA;QACAA;UACAmB;UACAD;UACAG;QACA;MACA;IACA;EAAA,GACA,yCACA;IACAC;MAAA;MACA;QACAtB;UACAmB;UACAD;UACAG;QACA;QACA;MACA;MACA;QACAE;MACA;MACA;QACAxB;QACA;UACAC;YACAmB;YACAD;YACAG;UACA;UACA;YACA;cACA,uCACAG;gBACAC;cAAA;YAEA;YACA;UACA;UACA;QACA;UACAzB;YACAmB;YACAD;YACAG;UACA;QACA;MACA;QACAtB;QACAC;UACAmB;UACAD;UACAG;QACA;MACA;IACA;IACAK;MACA;IACA;IACAC;MAAA;MACA;MACA5B;MACA;QACA;UACA;QACA;UACAA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;QACAA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IACA6B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEAC,aACA;gBACA;gBACA,yBACA,mBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA9B;gBACAC;kBACAkB;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAW;MAAA;MACA;QACA;UACA;UACA;QACA;UACA;QACA;QACA/B;MACA;IACA;IACAgC;MAAA;MACA;QACA;UACA;UACA;UACA;UACAhC;QACA;QACAA;MACA;IACA;IACAiC;MACA;QACAhC;UACAkB;UACAC;QACA;QACA;MACA;MACAnB;QACAgB;QACAC;UACAlB;UACAC;YACAkB;YACAC;UACA;QACA;MACA;IACA;IACAc;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAoC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAC;gBAAA;gBAAA,OACA;kBACAA;gBACA;cAAA;gBAFAF;gBAGA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAnC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAsC;MACArC;QACAgB;QACAC;UACAlB;QACA;MACA;IACA;IACAuC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;gBACA;kBACA;kBACA;oBAAA;kBAAA;kBACA;kBACA;oBAAA;kBAAA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAxC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAyC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAC;kBAAAC;kBAAAC;gBAAA;cAAA;gBAAAC;gBAEA;kBACA;kBACA;kBACA;kBACA;;kBAEA;kBACA;oBACAC;oBACAC;kBACA;kBAEA/C;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EAAA,EACA;EACAgD,4BACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAnE;MAAA;IAAA;IACAoE;MAAA;IAAA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACjjBA;AAAA;AAAA;AAAA;AAA6lD,CAAgB,ijDAAG,EAAC,C;;;;;;;;;;;ACAjnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/service.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/service.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./service.vue?vue&type=template&id=fe340868&scoped=true&\"\nvar renderjs\nimport script from \"./service.vue?vue&type=script&lang=js&\"\nexport * from \"./service.vue?vue&type=script&lang=js&\"\nimport style0 from \"./service.vue?vue&type=style&index=0&id=fe340868&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fe340868\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/service.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=template&id=fe340868&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-swiper/u-swiper\" */ \"uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n    uNoticeBar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-notice-bar/u-notice-bar\" */ \"uview-ui/components/u-notice-bar/u-notice-bar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\" ref=\"abc\">\r\n\t\t<!-- Modified: Added confirm popup with page load condition -->\r\n\t\t<view class=\"confirm-popup-overlay\" v-if=\"showConfirmPopup && isPageLoaded\">\r\n\t\t\t<view class=\"confirm-popup-container\">\r\n\t\t\t\t<view class=\"confirm-popup-content\">\r\n\t\t\t\t\t<view class=\"confirm-title\">活动规则</view>\r\n\t\t\t\t\t<view class=\"confirm-text\">\r\n\t\t\t\t\t\t1.本活动仅限安徽省阜阳市临泉县地区用户参与（以系统验证的定位信息或收货地址为准）<br>\r\n\t\t\t\t\t\t2. 活动时间：{{huodongdata.startTime}}，结束时间{{huodongdata.endTime}}（北京时间），仅限在此时间段内完成支付的订单可参与<br>\r\n\t\t\t\t\t\t3. 本次活动仅适用于<text style=\"color:#E8260D ;\">挂式空调清洗服务（不含中央空调、柜式空调等其他类型）</text>，服务包含标准内机清洁<br>\r\n\t\t\t\t\t\t4.\r\n\t\t\t\t\t\t每位用户限参与{{huodongdata.maxCount}}次（按用户手机号、支付账号和设备信息识别），服务排期将按支付时间顺序安排，支付成功后服务师傅将在48小时内跟您联系，请注意来电提醒。<br>\r\n\t\t\t\t\t\t5. 参与活动订单不可使用任何优惠券（专属活动优惠券除外），使用优惠券的订单将自动取消<br>\r\n\t\t\t\t\t\t6. 禁止通过虚假定位、技术刷单等非正常手段参与，违规订单将视为无效并取消服务资格，用户可在7个工作日内申诉 <br>\r\n\t\t\t\t\t\t7. 在法律允许范围内，平台保留对活动规则的最终解释及调整权利\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"confirm-buttons\">\r\n\t\t\t\t\t\t<view class=\"confirm-btn cancel\" @click=\"closeConfirmPopup\">取消</view>\r\n\t\t\t\t\t\t<view class=\"confirm-btn confirm\" @click=\"confirmToHuodongParity\">确认</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- Added: Login prompt popup -->\r\n\t\t<view class=\"confirm-popup-overlay\" v-if=\"showLoginPopup\">\r\n\t\t\t<view class=\"confirm-popup-container\">\r\n\t\t\t\t<view class=\"confirm-popup-content\">\r\n\t\t\t\t\t<view class=\"confirm-title\">登录提示</view>\r\n\t\t\t\t\t<view class=\"confirm-text\">\r\n\t\t\t\t\t\t您尚未登录，请先登录以继续参与活动。\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"confirm-buttons\">\r\n\t\t\t\t\t\t<view class=\"confirm-btn cancel\" @click=\"closeLoginPopup\">取消</view>\r\n\t\t\t\t\t\t<view class=\"confirm-btn confirm\" @click=\"goToLogin\">确认</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- Modified: Added page load condition -->\r\n\t\t<view class=\"large-promo-overlay\" v-if=\"showLargePromoPopup && district === '临泉县' && isPageLoaded\">\r\n\t\t\t<view class=\"large-promo-container\">\r\n\t\t\t\t<image :src=\"huodongdata.sharePictures\" class=\"promo-ac\" mode=\"widthFix\" />\r\n\t\t\t\t<!--  <view class=\"promo-background-area\">\r\n        <image src=\"../static/images/huodong.png\" class=\"promo-ac\" mode=\"widthFix\" />\r\n      </view> -->\r\n\r\n\t\t\t\t<view class=\"promo-foreground-area\">\r\n\t\t\t\t\t<view class=\"promo-price\">\r\n\t\t\t\t\t\t<!--  <text class=\"price-val\">{{huodongdata.payPrice}}</text>\r\n          <text class=\"price-unit\">元!!!</text> -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<!-- <view class=\"promo-subtitle\">\r\n          空调清洗秒杀中!\r\n        </view> -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"promo-button-area\" @click=\"showConfirm\">\r\n\t\t\t\t\t<image src=\"../static/images/yuyue.png\" mode=\"widthFix\" class=\"button-image\" />\r\n\t\t\t\t\t<view class=\"hand-pointer-animation\">\r\n\t\t\t\t\t\t<image src=\"/static/images/promo_hand.png\" mode=\"aspectFit\" class=\"hand-pointer-img\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"promo-close-btn\" @click=\"closeLargePromoPopup\">\r\n\t\t\t\t\t<uni-icons type=\"closeempty\" color=\"#fff\" size=\"18\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\r\n\t\t<!-- Modified: Adjusted transition and shrink behavior with page load condition -->\r\n\t\t<view class=\"activity-popup\" v-if=\"showActivityPopup && district === '临泉县' && isPageLoaded\" @click=\"showConfirm\"\r\n\t\t\t:class=\"{ 'activity-popup-shrunk': isPopupShrunk }\"\r\n\t\t\t:style=\"{ transform: isPopupShrunk ? 'translateX(60rpx)' : 'translateX(0)' }\">\r\n\t\t\t<view class=\"close-btn\" @click.stop=\"closeActivityPopup\">×</view>\r\n\t\t\t<view class=\"popup-content\">\r\n\t\t\t\t<view class=\"popup-text-main\">空调清洗</view>\r\n\t\t\t\t<image src=\"../static/images/kongtiao.png\" class=\"popup-header-image\" mode=\"aspectFit\"></image>\r\n\t\t\t\t<view class=\"popup-text-price\">\r\n\t\t\t\t\t<text class=\"price-number\">{{huodongdata.payPrice}}</text>秒杀\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"popup-action-btn\">\r\n\t\t\t\t\t立即抢\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<tabbar :cur=\"0\"></tabbar>\r\n\t\t<view class=\"content\">\r\n\t\t\t<view style=\"display: flex; align-items: center; justify-content: space-between;\">\r\n\t\t\t\t<image lazy-load src=\"../static/images/logo-index.jpg\" mode=\"aspectFit\"\r\n\t\t\t\t\tstyle=\"width: 50rpx; height: 50rpx; border-radius: 20%;\"></image>\r\n\t\t\t\t<view class=\"search_position\">\r\n\t\t\t\t\t<uni-icons type=\"location-filled\" size=\"20\"></uni-icons>\r\n\t\t\t\t\t<view class=\"position\">{{ position }}</view>\r\n\t\t\t\t\t<u-icon name=\"arrow-down-fill\" color=\"#ADADAD\" size=\"8\"></u-icon>\r\n\t\t\t\t\t<view class=\"shu\">丨</view>\r\n\t\t\t\t\t<uni-icons type=\"search\" size=\"20\" color=\"#ADADAD\"></uni-icons>\r\n\t\t\t\t\t<input type=\"text\" placeholder=\"空调维修\" @focus=\"goUrl('/user/search')\">\r\n\t\t\t\t\t<view class=\"btn\" @click=\"goUrl('/user/search')\">搜索</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"img\">\r\n\t\t\t\t<u-swiper :list=\"list1\" height=\"108\" :lazy-load=\"true\"></u-swiper>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"tag\">\r\n\t\t\t\t<view class=\"tag_item\"><text>就近师傅</text></view>丨\r\n\t\t\t\t<view class=\"tag_item\"><text>准时上门</text></view>丨\r\n\t\t\t\t<view class=\"tag_item\"><text>满意为止</text></view>丨\r\n\t\t\t\t<view class=\"tag_item\"><text>30天保修</text></view>\r\n\t\t\t\t<!-- <view class=\"tag_item\"><text>测试热更新</text></view> -->\r\n\t\t\t\t<!-- <view class=\"tag_item\" @click=\"tetxlogin\"><text>30天保修</text></view> -->\r\n\t\t\t</view>\r\n\t\t\t<view class=\"grid\">\r\n\t\t\t\t<view class=\"grid-container\">\r\n\t\t\t\t\t<view class=\"grid-item\" v-for=\"(baseListItem, baseListIndex) in baseList\" :key=\"baseListIndex\"\r\n\t\t\t\t\t\t@click=\"goUrl(baseListItem.link)\">\r\n\t\t\t\t\t\t<image lazy-load :src=\"baseListItem.img\" mode=\"aspectFit\"\r\n\t\t\t\t\t\t\tstyle=\"width: 96rpx; height: 94rpx; border-radius: 50%;\"></image>\r\n\t\t\t\t\t\t<text class=\"grid-text\">{{ baseListItem.title }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view>\r\n\t\t\t  <u-notice-bar :text=\"text1\" direction='column'  speed=\"1000\" ></u-notice-bar>\r\n\t\t\t</view>\r\n\t\t<!-- \t<view class=\"swiper\" id=\"id\">\r\n\t\t\t\t\t\r\n\t\t\t\t<span>公<text>告</text></span>\r\n\t\t\t\t<view class=\"shu\">丨</view>\r\n\t\t\t\t<u-notice-bar :text=\"text1\" url=\"\" direction=\"column\" bgColor=\"#fff\" color=\"#999\" :icon=\"null\"\r\n\t\t\t\t\tfontSize=\"12\"></u-notice-bar>\r\n\t\t\t</view> -->\r\n\t<!-- \t\t<view class=\"welfare\">\r\n\t\t\t\t<view class=\"top\">\r\n\t\t\t\t\t<view class=\"left\">\r\n\t\t\t\t\t\t<image lazy-load src=\"../static/images/4206.png\" mode=\"aspectFit\"\r\n\t\t\t\t\t\t\tstyle=\"width: 46rpx; height: 46rpx;\"></image>\r\n\t\t\t\t\t\t<text>限时福利,先到先得</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t\t<view class=\"btn\" @tap=\"goUrl('/pages/welfare')\">查看更多<u-icon name=\"play-right-fill\" color=\"#fff\"\r\n\t\t\t\t\t\t\tsize=\"8\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"bottom\">\r\n\t\t\t\t\t<image lazy-load src=\"../static/images/9467.png\" mode=\"aspectFit\"\r\n\t\t\t\t\t\tstyle=\"width: 170rpx; height: 224rpx;\"></image>\r\n\t\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t\t<view class=\"right_item\" v-for=\"(item, index) in couponList\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"box1\"><span>￥</span>{{ item.discount }}元</view>\r\n\t\t\t\t\t\t\t<view class=\"box2\">{{ item.full == 0 ? '通用券' : '满减券' }}</view>\r\n\t\t\t\t\t\t\t<view class=\"box3\" @click=\"debouncedGetlingqu(item)\"\r\n\t\t\t\t\t\t\t\t:style=\"item.haveGet == 1 ? 'background:#F2F3F4; color:#ADADAD;' : ''\">\r\n\t\t\t\t\t\t\t\t{{ item.haveGet == 1 ? '已领取' : '立即领取' }}\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"service\" v-for=\"(newItem, newIndex) in service\" :key=\"newIndex\">\r\n\t\t\t\t<view class=\"head\">\r\n\t\t\t\t\t<view class=\"left\">{{ newItem.name }}</view>\r\n\t\t\t\t\t<view class=\"right\" @click=\"goUrl(`/pages/technician?id=${newItem.id}`)\">\r\n\t\t\t\t\t\t查看更多\r\n\t\t\t\t\t\t<u-icon name=\"arrow-right\" color=\"#999999\" size=\"12\"></u-icon>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"se_main\">\r\n\t\t\t\t\t<view class=\"se_item\" v-for=\"(item, index) in newItem.serviceList\" :key=\"index\"\r\n\t\t\t\t\t\t@click=\"goUrl(`/user/commodity_details?id=${item.id}`)\">\r\n\t\t\t\t\t\t<image lazy-load :src=\"item.cover\" mode=\"aspectFit\"\r\n\t\t\t\t\t\t\tstyle=\"width: 290rpx; height: 286rpx; border-radius: 16rpx;\"></image>\r\n\t\t\t\t\t\t<view class=\"lbox\">\r\n\t\t\t\t\t\t\t<view class=\"name\">{{ item.title }}</view>\r\n\t\t\t\t\t\t\t<view class=\"baojia\">师傅报价</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"margin-top: 20rpx;\" class=\"\">\r\n\t\t\t\t<view class=\"tips\">家庭应急维修 首选今师傅</view>\r\n\t\t\t\t<view class=\"tips2\">200万+专业师傅 100万+附近服务网点 覆盖全国城市村镇</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tmapState,\r\n\t\tmapActions,\r\n\t\tmapMutations\r\n\t} from \"vuex\";\r\n\timport locationManager from '@/utils/location-manager.js';\r\n\timport tabbar from \"@/components/tabbar.vue\";\r\n\timport {\r\n\t\tdebounce\r\n\t} from \"lodash\";\r\n\timport { extractTokenFromHeaders } from '@/utils/cookieParser.js';\r\n\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\ttabbar,\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowLargePromoPopup: true,\r\n\t\t\t\tshowActivityPopup: true,\r\n\t\t\t\tshowConfirmPopup: false,\r\n\t\t\t\tshowLoginPopup: false, // Added: For login prompt popup\r\n\t\t\t\tlist1: [],\r\n\t\t\t\tservicecity:'',\r\n\t\t\t\tbannerList: [],\r\n\t\t\t\ttext1: [],\r\n\t\t\t\thuodongdata: '',\r\n\t\t\t\tshowmsg: '',\r\n\t\t\t\tdistrict: '',\r\n\t\t\t\tposition: \"\",\r\n\t\t\t\tbaseList: [],\r\n\t\t\t\tservice: [],\r\n\t\t\t\tcouponList: [],\r\n\t\t\t\tisLoading: false,\r\n\t\t\t\tisRefreshing: false,\r\n\t\t\t\tisServiceLoading: false,\r\n\t\t\t\tisPopupShrunk: false,\r\n\t\t\t\tscrollTimeout: null,\r\n\t\t\t\tisPageLoaded: false,\r\n\t\t\t};\r\n\t\t},\r\n\t\tasync onLoad(query) {\r\n\t\t\tconst scene = decodeURIComponent(query.scene || '');\r\n\t\t\tconsole.log('开始获取 scene:', scene);\r\n\t\t\tif (scene) {\r\n\t\t\t\tthis.$store.commit('setErweima', scene);\r\n\t\t\t\tuni.setStorageSync('erweima', scene);\r\n\t\t\t\tconsole.log('已存储 scene:', scene);\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log('未获取到 scene 参数');\r\n\t\t\t}\r\n\t\t\t// Check if large-promo-overlay was previously closed\r\n\t\t\tconst isLargePromoClosed = uni.getStorageSync('largePromoClosed');\r\n\t\t\tif (isLargePromoClosed) {\r\n\t\t\t\tthis.showLargePromoPopup = false;\r\n\t\t\t}\r\n\t\t\tawait this.initData();\r\n\t\t\tawait this.getBottom();\r\n\t\t},\r\n\t\tasync onPullDownRefresh() {\r\n\t\t\tconsole.log('开始下拉刷新');\r\n\t\t\tif (this.isRefreshing) {\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tthis.isRefreshing = true;\r\n\t\t\ttry {\r\n\t\t\t\tawait this.initData();\r\n\t\t\t\tawait this.getBottom();\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('刷新失败:', error);\r\n\t\t\t} finally {\r\n\t\t\t\tthis.isRefreshing = false;\r\n\t\t\t\tuni.stopPullDownRefresh();\r\n\t\t\t}\r\n\t\t},\r\n\t\tasync onShow() {\r\n\t\t\tconst autograph = uni.getStorageSync(\"autograph\");\r\n\t\t\tif (autograph && autograph !== \"\") {\r\n\t\t\t\tawait this.getSearchList();\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShareAppMessage() {},\r\n\t\tonPageScroll(e) {\r\n\t\t\tif (!this.isPopupShrunk) {\r\n\t\t\t\tthis.isPopupShrunk = true;\r\n\t\t\t}\r\n\t\t\tif (this.scrollTimeout) {\r\n\t\t\t\tclearTimeout(this.scrollTimeout);\r\n\t\t\t}\r\n\t\t\tthis.scrollTimeout = setTimeout(() => {\r\n\t\t\t\tthis.isPopupShrunk = false;\r\n\t\t\t}, 3000);\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t\t\r\n\t\r\n\t\t\t\r\n\t\t\t\r\n\t\t\tcloseLargePromoPopup() {\r\n\t\t\t\tthis.showLargePromoPopup = false;\r\n\t\t\t\t// Save the closed state to local storage\r\n\t\t\t\tuni.setStorageSync('largePromoClosed', true);\r\n\t\t\t},\r\n\t\t\tgrabDeal() {\r\n\t\t\t\tthis.showConfirm();\r\n\t\t\t},\r\n\t\t\tcloseActivityPopup() {\r\n\t\t\t\tthis.showActivityPopup = false;\r\n\t\t\t},\r\n\t\t\tshowConfirm() {\r\n\t\t\t\tthis.showConfirmPopup = true;\r\n\t\t\t},\r\n\t\t\tcloseConfirmPopup() {\r\n\t\t\t\tthis.showConfirmPopup = false;\r\n\t\t\t},\r\n\t\t\tcloseLoginPopup() { // Added: Close login popup\r\n\t\t\t\tthis.showLoginPopup = false;\r\n\t\t\t},\r\n\t\t\tgoToLogin() { // Added: Navigate to login page\r\n\t\t\t\tthis.showLoginPopup = false;\r\n\t\t\t\tthis.showConfirmPopup = false;\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/mine',\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error(\"导航到登录页面失败:\", err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"跳转失败\",\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tconfirmToHuodongParity() {\r\n\t\t\t\t// Check if user is logged in by retrieving token\r\n\t\t\t\tconst token = uni.getStorageSync('token');\r\n\t\t\t\tif (!token || token === '') {\r\n\t\t\t\t\tthis.showLoginPopup = true;\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Existing logic if token exists\r\n\t\t\t\tthis.showConfirmPopup = false;\r\n\t\t\t\tthis.showLargePromoPopup = false;\r\n\t\t\t\t// Save the closed state to local storage when confirming\r\n\t\t\t\tuni.setStorageSync('largePromoClosed', true);\r\n\t\t\t\tif (this.isPageLoaded) {\r\n\t\t\t\t\tthis.goUrl('/user/huodong_parity?id=519&type=1');\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: this.showmsg,\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t...mapActions([\"serviceCate\"]),\r\n\t\t\t...mapMutations([\"updatePosition\"]),\r\n\t\t\tgetlingqu(item) {\r\n\t\t\t\tif (item.haveGet == 1) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\ttitle: '已领取过了',\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tconst obj = {\r\n\t\t\t\t\tcouponId: [item.id]\r\n\t\t\t\t};\r\n\t\t\t\tthis.$api.service.getWelfare(obj).then(res => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tif (res.code === \"200\") {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\ttitle: '领取成功',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.couponList = this.couponList.map(coupon => {\r\n\t\t\t\t\t\t\tif (coupon.id === item.id) {\r\n\t\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t\t...coupon,\r\n\t\t\t\t\t\t\t\t\thaveGet: 1\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\treturn coupon;\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tthis.getCoupon();\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ticon: 'error',\r\n\t\t\t\t\t\t\ttitle: '领取失败',\r\n\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tconsole.error(\"领取优惠券失败:\", err);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ticon: 'error',\r\n\t\t\t\t\t\ttitle: err,\r\n\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tdebouncedGetlingqu: debounce(function(item) {\r\n\t\t\t\tthis.getlingqu(item);\r\n\t\t\t}, 1000),\r\n\t\t\tgetBottom() {\r\n\t\t\t\tthis.isServiceLoading = true;\r\n\t\t\t\tconsole.log(this.servicecity)\r\n\t\t\t\treturn this.$api.service.getBottom('阜阳市').then(res => {\r\n\t\t\t\t\tif (res && Array.isArray(res.data)) {\r\n\t\t\t\t\t\tthis.service = res.data;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.warn(\"getBottom 返回数据无效或为空:\", res);\r\n\t\t\t\t\t\tthis.service = [];\r\n\t\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t\t// \ttitle: \"服务数据加载失败\",\r\n\t\t\t\t\t\t// \ticon: \"none\",\r\n\t\t\t\t\t\t// });\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tconsole.error(\"获取底部数据失败:\", err);\r\n\t\t\t\t\tthis.service = [];\r\n\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t// \ttitle: \"服务数据加载失败\",\r\n\t\t\t\t\t// \ticon: \"none\",\r\n\t\t\t\t\t// });\r\n\t\t\t\t}).finally(() => {\r\n\t\t\t\t\tthis.isServiceLoading = false;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tasync initData() {\r\n\t\t\t\tif (this.isLoading) return;\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait Promise.all([\r\n\t\t\t\t\t\tthis.copyMethod(),\r\n\t\t\t\t\t\t// this.gethuodongconfig(),\r\n\t\t\t\t\t\tthis.getNowPosition(),\r\n\t\t\t\t\t\tthis.getCoupon(),\r\n\t\t\t\t\t]);\r\n\t\t\t\t} catch (err) {\r\n\t\t\t\t\tconsole.error(\"初始化数据失败:\", err);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"数据加载失败\",\r\n\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t});\r\n\t\t\t\t} finally {\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetcount() {\r\n\t\t\t\tthis.$api.service.huodongcount().then(res => {\r\n\t\t\t\t\tif (res.code === \"-1\") {\r\n\t\t\t\t\t\tthis.isPageLoaded = false;\r\n\t\t\t\t\t\tthis.showmsg = res.msg\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.isPageLoaded = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgethuodongconfig() {\r\n\t\t\t\tthis.$api.service.huodongselectActivityConfig().then(res => {\r\n\t\t\t\t\tif (res.code === \"200\") {\r\n\t\t\t\t\t\tthis.getcount()\r\n\t\t\t\t\t\tthis.huodongdata = res.data\r\n\t\t\t\t\t\tthis.isPageLoaded = true;\r\n\t\t\t\t\t\tconsole.log(this.huodongdata)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgodetils(id) {\r\n\t\t\t\tif (!id) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"无效的商品 ID\",\r\n\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/details/details?id=${id}`,\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error(\"跳转失败:\", err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"跳转失败: \" + err.errMsg,\r\n\t\t\t\t\t\t\ticon: \"none\",\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tasync getSearchList() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res = await this.$api.service.getHotSearch();\r\n\t\t\t\t\tthis.SearchList = res.length > 3 ? res.slice(0, 3) : res;\r\n\t\t\t\t} catch (err) {\r\n\t\t\t\t\tconsole.error(\"获取热搜失败:\", err);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getCoupon() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tlet userId = uni.getStorageSync('userId');\r\n\t\t\t\t\tconst res = await this.$api.service.getWelfareList({\r\n\t\t\t\t\t\tuserId: userId\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.couponList = res.data.list.slice(0, 3);\r\n\t\t\t\t} catch (err) {\r\n\t\t\t\t\tconsole.error(\"获取优惠券失败:\", err);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoUrl(e) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: e,\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error(\"导航失败:\", err);\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tasync copyMethod() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst res4 = await this.$api.service.getIndex();\r\n\t\t\t\t\t// const res5 = await this.$api.service.getServiceCate();\r\n\t\t\t\t\tif (res4.code === \"200\") {\r\n\t\t\t\t\t\tthis.bannerList = res4.data.banner;\r\n\t\t\t\t\t\tthis.list1 = res4.data.banner.map((item) => item.img);\r\n\t\t\t\t\t\tthis.baseList = res4.data.jingang;\r\n\t\t\t\t\t\tthis.text1 = res4.data.notices.map((item) => item.content);\r\n\t\t\t\t\t\tthis.service = res4.data.serviceCate;\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (err) {\r\n\t\t\t\t\tconsole.error(\"获取首页数据失败:\", err);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync getNowPosition() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 使用统一的定位管理器，避免重复调用\r\n\t\t\t\t\tconst locationData = await locationManager.getLocation({ forceUpdate: false, silent: true });\r\n\r\n\t\t\t\t\tif (locationData) {\r\n\t\t\t\t\t\t// 更新页面数据\r\n\t\t\t\t\t\tthis.district = locationData.district || '';\r\n\t\t\t\t\t\tthis.servicecity = locationData.city || '';\r\n\t\t\t\t\t\tthis.position = locationData.city || '';\r\n\r\n\t\t\t\t\t\t// 更新 Vuex store\r\n\t\t\t\t\t\tthis.updatePosition({\r\n\t\t\t\t\t\t\tkey: \"position\",\r\n\t\t\t\t\t\t\tval: this.position,\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\tconsole.log(\"定位获取成功:\", locationData);\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error(\"获取定位失败:\", error);\r\n\t\t\t\t\t// 定位失败不影响页面功能\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t...mapState({\r\n\t\t\t\tprimaryColor: (state) => state.config.configInfo.primaryWidth,\r\n\t\t\t\tsubColor: (state) => state.config.configInfo.subColor,\r\n\t\t\t\tconfigInfo: (state) => state.config.configInfo,\r\n\t\t\t\tcommonOptions: (state) => state.user.commonOptions,\r\n\t\t\t\tuserInfo: (state) => state.user.userInfo,\r\n\t\t\t\tuserPageType: (state) => state.user.userPageType,\r\n\t\t\t\tmineInfo: (state) => state.user.mineInfo,\r\n\t\t\t\tposition: (state) => state.position,\r\n\t\t\t\tserviceCateData: (state) => state.serviceCateData,\r\n\t\t\t}),\r\n\t\t},\r\n\t};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t/* Added: Styles for confirm popup */\r\n\t.confirm-popup-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100vw;\r\n\t\theight: 100vh;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.7);\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tz-index: 1002;\r\n\t\ttransition: opacity 0.3s ease-in-out;\r\n\t}\r\n\r\n\t.confirm-popup-container {\r\n\t\twidth: 600rpx;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 40rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.confirm-popup-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.confirm-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.confirm-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tline-height: 40rpx;\r\n\t\ttext-align: left;\r\n\t\twidth: 100%;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\r\n\t.confirm-buttons {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.confirm-btn {\r\n\t\twidth: 200rpx;\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.confirm-btn.cancel {\r\n\t\tbackground: #f2f3f4;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.confirm-btn.confirm {\r\n\t\tbackground: linear-gradient(270deg, #EA5533 0%, #EE8751 100%);\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t/* Existing styles */\r\n\t.large-promo-overlay,\r\n\t.activity-popup {\r\n\t\ttransition: opacity 0.3s ease-in-out;\r\n\t}\r\n\r\n\t.large-promo-overlay[hidden],\r\n\t.activity-popup[hidden] {\r\n\t\topacity: 0;\r\n\t\tpointer-events: none;\r\n\t}\r\n\r\n\t.content {\r\n\t\ttransition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;\r\n\t}\r\n\r\n\t.content[hidden] {\r\n\t\topacity: 0;\r\n\t\ttransform: translateY(20rpx);\r\n\t}\r\n\r\n\t@keyframes hand-press {\r\n\t\t0% {\r\n\t\t\ttransform: scale(1);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: scale(0.9);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: scale(1);\r\n\t\t}\r\n\t}\r\n\r\n\t.pulse-text {\r\n\t\tanimation: pulse-animation 0.5s infinite ease-in-out;\r\n\t}\r\n\r\n\t.large-promo-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100vw;\r\n\t\theight: 100vh;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.7);\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tz-index: 1001;\r\n\t}\r\n\r\n\t.large-promo-container {\r\n\t\tposition: relative;\r\n\t\twidth: 600rpx;\r\n\t\theight: 480rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.promo-background-area {\r\n\t\tposition: absolute;\r\n\t\ttop: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 252rpx;\r\n\t\tbackground: radial-gradient(circle at 50% 120%, #ff8964, #dd4a34);\r\n\t\tborder-radius: 14rpx;\r\n\t}\r\n\r\n\t.promo-ac {\r\n\t\tposition: absolute;\r\n\t\ttop: -100rpx;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\twidth: 450rpx;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t.promo-character {\r\n\t\tposition: absolute;\r\n\t\tbottom: -6rpx;\r\n\t\tright: 18rpx;\r\n\t\twidth: 132rpx;\r\n\t\tz-index: 3;\r\n\t}\r\n\r\n\t.promo-coin {\r\n\t\tposition: absolute;\r\n\t\tbottom: 72rpx;\r\n\t\tleft: 24rpx;\r\n\t\twidth: 54rpx;\r\n\t\tz-index: 3;\r\n\t}\r\n\r\n\t.promo-foreground-area {\r\n\t\tposition: absolute;\r\n\t\ttop: 200rpx;\r\n\t\twidth: 348rpx;\r\n\t\theight: 180rpx;\r\n\t\tborder-radius: 24rpx;\r\n\t\tz-index: 2;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tpadding-top: 24rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.promo-price {\r\n\t\tcolor: #f82c28;\r\n\t\tfont-weight: 900;\r\n\t\tdisplay: flex;\r\n\t\talign-items: baseline;\r\n\t\tline-height: 1;\r\n\t\ttext-shadow: -1px -1px 0 #fff, 1px -1px 0 #fff, -1px 1px 0 #fff, 1px 1px 0 #fff;\r\n\t}\r\n\r\n\t.price-val {\r\n\t\tfont-size: 96rpx;\r\n\t}\r\n\r\n\t.price-unit {\r\n\t\tfont-size: 30rpx;\r\n\t\tmargin-left: 6rpx;\r\n\t}\r\n\r\n\t.promo-subtitle {\r\n\t\tmargin-top: 12rpx;\r\n\t\tbackground-color: #fadda6;\r\n\t\tcolor: #f82c28;\r\n\t\tfont-size: 22rpx;\r\n\t\tfont-weight: bold;\r\n\t\tpadding: 5rpx 18rpx;\r\n\t\tborder-radius: 18rpx;\r\n\t}\r\n\r\n\t.promo-button-area {\r\n\t\tposition: absolute;\r\n\t\tbottom: -10rpx;\r\n\t\tz-index: 5;\r\n\t\twidth: 450rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: bold;\r\n\t\ttext-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);\r\n\t\tcursor: pointer;\r\n\t\tanimation: pulse-animation 0.5s infinite ease-in-out;\r\n\t}\r\n\r\n\t.button-image {\r\n\t\twidth: 320rpx !important;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.hand-pointer-animation {\r\n\t\tposition: absolute;\r\n\t\tright: 30rpx;\r\n\t\tbottom: -12rpx;\r\n\t\tanimation: hand-press 1.5s infinite;\r\n\t}\r\n\r\n\t.hand-pointer-img {\r\n\t\twidth: 48rpx;\r\n\t\theight: 48rpx;\r\n\t}\r\n\r\n\t.promo-close-btn {\r\n\t\tposition: absolute;\r\n\t\tbottom: -80rpx;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\twidth: 42rpx;\r\n\t\theight: 42rpx;\r\n\t\tborder: 2rpx solid rgba(255, 255, 255, 0.7);\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tz-index: 4;\r\n\t}\r\n\r\n\t@keyframes pulse-animation {\r\n\t\t0% {\r\n\t\t\ttransform: scale(1);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: scale(1.05);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: scale(1);\r\n\t\t}\r\n\t}\r\n\r\n\t.activity-popup {\r\n\t\tposition: fixed;\r\n\t\tright: 16rpx;\r\n\t\tbottom: 600rpx;\r\n\t\tz-index: 999;\r\n\t\twidth: 120rpx;\r\n\t\tbackground: linear-gradient(180deg, #FFE1C1 0%, #FF7B5A 100%);\r\n\t\tborder-radius: 12rpx;\r\n\t\tborder: 2rpx solid #FFF;\r\n\t\tbox-shadow: 0rpx 4rpx 8rpx rgba(0, 0, 0, 0.2);\r\n\t\tpadding: 6rpx;\r\n\t\tcursor: pointer;\r\n\t\ttransition: transform 0.3s ease-in-out;\r\n\t}\r\n\r\n\t.activity-popup-shrunk {}\r\n\r\n\t.activity-popup-shrunk .popup-content {\r\n\t\topacity: 1;\r\n\t}\r\n\r\n\t.popup-content {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\ttext-align: center;\r\n\t\ttransition: opacity 0.3s ease-in-out;\r\n\t}\r\n\r\n\t.popup-header-image {\r\n\t\twidth: 70rpx;\r\n\t\theight: 40rpx;\r\n\t\tmargin-top: 4rpx;\r\n\t}\r\n\r\n\t.popup-text-main {\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #A34A00;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-top: 6rpx;\r\n\t}\r\n\r\n\t.popup-text-price {\r\n\t\tfont-size: 18rpx;\r\n\t\tcolor: #E53935;\r\n\t\tmargin-top: 2rpx;\r\n\t\tfont-weight: 500;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\r\n\t.price-number {\r\n\t\tfont-size: 24rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.popup-action-btn {\r\n\t\twidth: 90rpx;\r\n\t\tmargin-top: 8rpx;\r\n\t\tmargin-bottom: 4rpx;\r\n\t\tpadding: 6rpx 0;\r\n\t\tbackground: linear-gradient(270deg, #FF5A36 0%, #F60100 100%);\r\n\t\tborder-radius: 18rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 18rpx;\r\n\t\tfont-weight: bold;\r\n\t\ttext-align: center;\r\n\t\tanimation: pulse-animation 0.5s infinite ease-in-out;\r\n\t}\r\n\r\n\t.close-btn {\r\n\t\tposition: absolute;\r\n\t\ttop: -14rpx;\r\n\t\tright: -14rpx;\r\n\t\twidth: 28rpx;\r\n\t\theight: 28rpx;\r\n\t\tbackground-color: #888888;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 50%;\r\n\t\tborder: 2rpx solid #fff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 20rpx;\r\n\t\tline-height: 28rpx;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.page {\r\n\t\tbackground-color: #599EFF;\r\n\t\tmin-height: 100vh;\r\n\t\tpadding-bottom: 80rpx;\r\n\t}\r\n\r\n\t.content {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 40rpx 40rpx 0 0;\r\n\t\tpadding: 0 30rpx;\r\n\t\tpadding-top: 18rpx;\r\n\t\tpadding-bottom: 50rpx;\r\n\t\toverflow: auto;\r\n\t}\r\n\r\n\t.search_position {\r\n\t\twidth: 630rpx;\r\n\t\theight: 72rpx;\r\n\t\tbackground: #F1F1F1;\r\n\t\tborder-radius: 36rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 0 15rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.position {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333333;\r\n\t\tmargin: 0 12rpx;\r\n\t}\r\n\r\n\t.shu {\r\n\t\tmargin: 0 15rpx;\r\n\t\tcolor: rgba(0, 0, 0, 0.2);\r\n\t}\r\n\r\n\tinput {\r\n\t\tmargin-left: 22rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #ADADAD;\r\n\t\twidth: 260rpx;\r\n\t}\r\n\r\n\t.btn {\r\n\t\twidth: 112rpx;\r\n\t\theight: 56rpx;\r\n\t\tbackground: #2E80FE;\r\n\t\tborder-radius: 28rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tline-height: 56rpx;\r\n\t\ttext-align: center;\r\n\t\tposition: absolute;\r\n\t\tright: 20rpx;\r\n\t}\r\n\r\n\t.img {\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.tag {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\tcolor: #BDD4FD;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.tag_item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.tag_item text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333333;\r\n\t\tmargin-left: 14rpx;\r\n\t}\r\n\r\n\t.grid {\r\n\t\tmargin-top: 40rpx;\r\n\t}\r\n\r\n\t.grid-container {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.grid-item {\r\n\t\twidth: 20%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tpadding: 10rpx 0;\r\n\t}\r\n\r\n\t.swiper {\r\n\t\twidth: 690rpx;\r\n\t\theight: 90rpx;\r\n\t\tmargin-top: 40rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 0 32rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\tborder: 2rpx solid #F7F6F6;\r\n\t}\r\n\r\n\t.swiper span {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #000;\r\n\t}\r\n\r\n\t.swiper span text {\r\n\t\tcolor: #E72427;\r\n\t}\r\n\r\n\t.swiper .shu {\r\n\t\tfont-weight: 200;\r\n\t\tcolor: #E6E6E6;\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.welfare {\r\n\t\tmargin-top: 38rpx;\r\n\t\twidth: 690rpx;\r\n\t\theight: 325rpx;\r\n\t\tbackground-image: url(\"../static/images/9243.png\");\r\n\t\tbackground-size: cover;\r\n\t\tpadding: 22rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.welfare .top {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.welfare .top .left {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.welfare .top .left text {\r\n\t\tmargin-left: 16rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #451815;\r\n\t}\r\n\r\n\t.welfare .top .btn {\r\n\t\twidth: 124rpx;\r\n\t\theight: 46rpx;\r\n\t\tbackground: linear-gradient(270deg, #EA5533 0%, #EE8751 100%);\r\n\t\tborder-radius: 24rpx;\r\n\t\tfont-size: 20rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 14rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.welfare .bottom {\r\n\t\twidth: 646rpx;\r\n\t\tmargin-top: 12rpx;\r\n\t\tdisplay: flex;\r\n\t\tborder-radius: 12rpx;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.welfare .bottom .right {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.welfare .bottom .right .right_item {\r\n\t\twidth: 136rpx;\r\n\t\theight: 198rpx;\r\n\t\tbackground: linear-gradient(180deg, #FEF7EC 0%, #FCEFDF 100%);\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding-top: 28rpx;\r\n\t}\r\n\r\n\t.welfare .bottom .right .right_item .box1 {\r\n\t\tfont-size: 35rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #E55138;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.welfare .bottom .right .right_item .box1 span {\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.welfare .bottom .right .right_item .box2 {\r\n\t\tmargin-top: 12rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #E55138;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.welfare .bottom .right .right_item .box3 {\r\n\t\tmargin: 12rpx auto 0;\r\n\t\twidth: 98rpx;\r\n\t\theight: 36rpx;\r\n\t\tbackground: linear-gradient(270deg, #EA5533 0%, #EE8751 100%);\r\n\t\tborder-radius: 18rpx;\r\n\t\tfont-size: 16rpx;\r\n\t\tcolor: #FFFFFF;\r\n\t\tline-height: 36rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.service {\r\n\t\tmargin-top: 40rpx;\r\n\t}\r\n\r\n\t.service .head {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.service .head .left {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.service .head .right {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.service .se_main {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tflex-wrap: wrap;\r\n\t\tmargin-top: 20rpx;\r\n\t}\r\n\r\n\t.service .se_main .se_item {\r\n\t\twidth: 336rpx;\r\n\t\theight: 428rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 20rpx;\r\n\t\tborder: 2rpx solid #F3F3F3;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: center;\r\n\t\tpadding-top: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.service .se_main .se_item .lbox {\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.service .se_main .se_item .lbox .name {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #333333;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.service .se_main .se_item .lbox .baojia {\r\n\t\tmargin: 6rpx auto 0;\r\n\t\twidth: 140rpx;\r\n\t\theight: 50rpx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 2rpx solid #2E80FE;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #2E80FE;\r\n\t\tline-height: 50rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.tips {\r\n\t\tmargin-top: 32rpx;\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 500;\r\n\t\tcolor: #333333;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.tips2 {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333333;\r\n\t\tmargin-top: 16rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=style&index=0&id=fe340868&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./service.vue?vue&type=style&index=0&id=fe340868&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755670625861\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}