@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.u-line-1 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical !important;
}
.u-line-2 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical !important;
}
.u-line-3 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical !important;
}
.u-line-4 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical !important;
}
.u-line-5 {
  display: -webkit-box !important;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical !important;
}
.u-border {
  border-width: 0.5px !important;
  border-color: #dadbde !important;
  border-style: solid;
}
.u-border-top {
  border-top-width: 0.5px !important;
  border-color: #dadbde !important;
  border-top-style: solid;
}
.u-border-left {
  border-left-width: 0.5px !important;
  border-color: #dadbde !important;
  border-left-style: solid;
}
.u-border-right {
  border-right-width: 0.5px !important;
  border-color: #dadbde !important;
  border-right-style: solid;
}
.u-border-bottom {
  border-bottom-width: 0.5px !important;
  border-color: #dadbde !important;
  border-bottom-style: solid;
}
.u-border-top-bottom {
  border-top-width: 0.5px !important;
  border-bottom-width: 0.5px !important;
  border-color: #dadbde !important;
  border-top-style: solid;
  border-bottom-style: solid;
}
.u-reset-button {
  padding: 0;
  background-color: transparent;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}
.u-reset-button::after {
  border: none;
}
.u-hover-class {
  opacity: 0.7;
}
.u-primary-light {
  color: #ecf5ff;
}
.u-warning-light {
  color: #fdf6ec;
}
.u-success-light {
  color: #f5fff0;
}
.u-error-light {
  color: #fef0f0;
}
.u-info-light {
  color: #f4f4f5;
}
.u-primary-light-bg {
  background-color: #ecf5ff;
}
.u-warning-light-bg {
  background-color: #fdf6ec;
}
.u-success-light-bg {
  background-color: #f5fff0;
}
.u-error-light-bg {
  background-color: #fef0f0;
}
.u-info-light-bg {
  background-color: #f4f4f5;
}
.u-primary-dark {
  color: #398ade;
}
.u-warning-dark {
  color: #f1a532;
}
.u-success-dark {
  color: #53c21d;
}
.u-error-dark {
  color: #e45656;
}
.u-info-dark {
  color: #767a82;
}
.u-primary-dark-bg {
  background-color: #398ade;
}
.u-warning-dark-bg {
  background-color: #f1a532;
}
.u-success-dark-bg {
  background-color: #53c21d;
}
.u-error-dark-bg {
  background-color: #e45656;
}
.u-info-dark-bg {
  background-color: #767a82;
}
.u-primary-disabled {
  color: #9acafc;
}
.u-warning-disabled {
  color: #f9d39b;
}
.u-success-disabled {
  color: #a9e08f;
}
.u-error-disabled {
  color: #f7b2b2;
}
.u-info-disabled {
  color: #c4c6c9;
}
.u-primary {
  color: #3c9cff;
}
.u-warning {
  color: #f9ae3d;
}
.u-success {
  color: #5ac725;
}
.u-error {
  color: #f56c6c;
}
.u-info {
  color: #909399;
}
.u-primary-bg {
  background-color: #3c9cff;
}
.u-warning-bg {
  background-color: #f9ae3d;
}
.u-success-bg {
  background-color: #5ac725;
}
.u-error-bg {
  background-color: #f56c6c;
}
.u-info-bg {
  background-color: #909399;
}
.u-main-color {
  color: #303133;
}
.u-content-color {
  color: #606266;
}
.u-tips-color {
  color: #909193;
}
.u-light-color {
  color: #c0c4cc;
}
.u-safe-area-inset-top {
  padding-top: 0;
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}
.u-safe-area-inset-right {
  padding-right: 0;
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
.u-safe-area-inset-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.u-safe-area-inset-left {
  padding-left: 0;
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
}
::-webkit-scrollbar {
  display: none;
  width: 0 !important;
  height: 0 !important;
  -webkit-appearance: none;
  background: transparent;
}
/* 1px方案,改变border的颜色即可 */
.b-1px, .b-1px-t, .b-1px-b, .b-1px-tb, .b-1px-l, .b-1px-r {
  position: relative;
}
.b-1px:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  width: 200%;
  border: 1px solid #eee;
  color: #eee;
  height: 200%;
  -webkit-transform-origin: left top;
          transform-origin: left top;
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
  box-sizing: border-box;
}
.b-1px-t:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #eee;
  color: #eee;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  -webkit-transform: scaley(0.5);
          transform: scaley(0.5);
  box-sizing: border-box;
}
.b-1px-b:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #eee;
  color: #eee;
  -webkit-transform-origin: 0 100%;
          transform-origin: 0 100%;
  -webkit-transform: scaley(0.5);
          transform: scaley(0.5);
  box-sizing: border-box;
}
.b-1px-tb:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #eee;
  color: #eee;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  -webkit-transform: scaley(0.5);
          transform: scaley(0.5);
  box-sizing: border-box;
}
.b-1px-tb:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #eee;
  color: #eee;
  -webkit-transform-origin: 0 100%;
          transform-origin: 0 100%;
  -webkit-transform: scaley(0.5);
          transform: scaley(0.5);
  box-sizing: border-box;
}
.b-1px-l::before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  bottom: 0;
  border-left: 1px solid #eee;
  color: #eee;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
  -webkit-transform: scalex(0.5);
          transform: scalex(0.5);
  box-sizing: border-box;
}
.b-1px-r::after {
  content: " ";
  position: absolute;
  right: 0;
  top: 0;
  width: 1px;
  bottom: 0;
  border-right: 1px solid #eee;
  color: #eee;
  -webkit-transform-origin: 100% 0;
          transform-origin: 100% 0;
  -webkit-transform: scalex(0.5);
          transform: scalex(0.5);
  box-sizing: border-box;
}
/* 头像 */
.avatar {
  margin: 0;
  padding: 0;
  display: inline-flex;
  text-align: center;
  justify-content: center;
  align-items: center;
  background: #f4f6f8;
  color: #fff;
  white-space: nowrap;
  position: relative;
  width: 120rpx;
  height: 120rpx;
  background-size: cover;
  background-position: center;
  vertical-align: middle;
}
.avatar.lg {
  width: 160rpx;
  height: 160rpx;
}
.avatar.md {
  width: 100rpx;
  height: 100rpx;
}
.avatar.sm {
  width: 80rpx;
  height: 80rpx;
}
.avatar-group {
  direction: ltl;
  unicode-bidi: bidi-override;
  display: inline-block;
}
.avatar-group .avatar {
  width: 48rpx;
  height: 48rpx;
  font-size: 1em;
  border-radius: 50%;
  margin-left: -20rpx;
  border: 4rpx solid white;
  vertical-align: middle;
  -webkit-transform: rotateZ(360deg);
          transform: rotateZ(360deg);
}
.avatar-group .avatar:nth-child(1) {
  margin-left: 0rpx;
}
/* 字体大小 */
.f-little {
  font-size: 18rpx;
}
.f-icontext {
  font-size: 22rpx;
}
/* 很小的文字,一般和图标一起使用 */
.f-caption {
  font-size: 24rpx;
}
/* 辅助描述性文字 */
.f-desc {
  font-size: 26rpx;
}
/* 段落字体 */
.f-paragraph {
  font-size: 28rpx;
}
/* 段落字体 */
.f-title {
  font-size: 32rpx;
}
/* 标题 */
.f-sm-title {
  font-size: 36rpx;
}
/* 大点的标题 */
.f-md-title {
  font-size: 40rpx;
}
/* 大点的标题 */
.f-lg-title {
  font-size: 42rpx;
}
/* 大点的标题 */
/* 字体颜色 */
.c-base {
  color: #ffffff;
}
/* 白色 */
.c-black {
  color: #000000;
}
/* 黑色 */
.c-title {
  color: #232A24;
}
/* 标题/副标题 */
.c-desc {
  color: #3D2C1B;
}
/* 辅助描述性文字 */
.c-caption {
  color: #999999;
}
/* 辅助描述性文字 */
.c-paragraph {
  color: #666666;
}
/* 段落字体 */
.c-success {
  color: #1BCA62;
}
/* 成功/链接文字 */
.c-tips {
  color: #ffd753;
}
/* 失效 */
.c-warning {
  color: #FF2404;
}
/* 警告/非法 */
.c-nodata {
  color: #cccccc;
}
/* 链接文字 */
.c-shadow {
  text-shadow: 2rpx 2rpx 2rpx #808080;
}
/* 字体阴影 */
/* 填充色 */
.fill-base {
  background: #ffffff;
}
/* 默认 */
.fill-body {
  background: #f7f7f7;
}
/* 页面 */
.fill-primary {
  background: #599eff;
}
/* 主题色/主要活动按钮 */
.fill-caption {
  background: #ffd753;
}
/* 辅助色 */
.fill-warning {
  background: #f12c20;
}
/* 警告/非法 */
.fill-second {
  background: #efeff4;
}
/* 区块分割线 */
.fill-space {
  background: #FCFCFC;
}
/* 次要活动按钮 */
/* 阴影 */
.box-shadow {
  box-shadow: 0px 3px 6px 0px rgba(227, 227, 227, 0.47);
}
.box-shadow-mini {
  box-shadow: 2rpx 0 10rpx rgba(4, 0, 0, 0.08);
}
/* 字体样式 */
text {
  vertical-align: middle;
}
/* 上下居中 */
.text-left {
  text-align: left;
}
/* 左对齐 */
.text-center {
  text-align: center;
}
/* 中对齐 */
.text-right {
  text-align: right;
}
/* 右对齐 */
.text-justify {
  text-align: justify;
}
/* 两端对齐，谨慎使用 */
.text-justify::after {
  content: '';
  width: 100%;
  display: inline-block;
}
.text-delete {
  text-decoration: line-through;
}
/* 删除线 */
.text-underline {
  text-decoration: underline;
}
/* 下划线 */
.text-bold {
  font-weight: bold;
}
/* 加粗 */
.text-normal {
  font-weight: normal;
}
/* 文本溢出省略 */
.ellipsis {
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.ellipsis-3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}
.ellipsis-4 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
}
/* 最大宽度 */
.max-100 {
  max-width: 100rpx;
}
.max-150 {
  max-width: 150rpx;
}
.max-200 {
  max-width: 200rpx;
}
.max-270 {
  max-width: 270rpx;
}
.max-300 {
  max-width: 300rpx;
}
.max-350 {
  max-width: 350rpx;
}
.max-380 {
  max-width: 380rpx;
}
.max-400 {
  max-width: 400rpx;
}
.max-446 {
  max-width: 446rpx;
}
.max-500 {
  max-width: 500rpx;
}
.max-510 {
  max-width: 510rpx;
}
.max-520 {
  max-width: 520rpx;
}
.max-540 {
  max-width: 540rpx;
}
.max-550 {
  max-width: 550rpx;
}
.max-566 {
  max-width: 566rpx;
}
.max-580 {
  max-width: 580rpx;
}
/* 外间距 */
.mg-sm {
  margin: 10rpx;
}
.mg-md {
  margin: 20rpx;
}
.mg-lg {
  margin: 30rpx;
}
.mt-sm {
  margin-top: 10rpx;
}
.mt-md {
  margin-top: 20rpx;
}
.mt-lg {
  margin-top: 30rpx;
}
.mr-sm {
  margin-right: 10rpx;
}
.mr-md {
  margin-right: 20rpx;
}
.mr-lg {
  margin-right: 30rpx;
}
.mb-sm {
  margin-bottom: 10rpx;
}
.mb-md {
  margin-bottom: 20rpx;
}
.mb-lg {
  margin-bottom: 30rpx;
}
.ml-sm {
  margin-left: 10rpx;
}
.ml-md {
  margin-left: 20rpx;
}
.ml-lg {
  margin-left: 30rpx;
}
/* 内间距 */
.pd-sm {
  padding: 10rpx;
}
.pd-md {
  padding: 20rpx;
}
.pd-lg {
  padding: 30rpx;
}
.pt-sm {
  padding-top: 10rpx;
}
.pt-md {
  padding-top: 20rpx;
}
.pt-lg {
  padding-top: 30rpx;
}
.pr-sm {
  padding-right: 10rpx;
}
.pr-md {
  padding-right: 20rpx;
}
.pr-lg {
  padding-right: 30rpx;
}
.pb-sm {
  padding-bottom: 10rpx;
}
.pb-md {
  padding-bottom: 20rpx;
}
.pb-lg {
  padding-bottom: 30rpx;
}
.pl-sm {
  padding-left: 10rpx;
}
.pl-md {
  padding-left: 20rpx;
}
.pl-lg {
  padding-left: 30rpx;
}
/* 图标尺寸 */
.icon-xs {
  width: 32rpx;
  height: 32rpx;
  display: block;
  font-size: 32rpx;
}
.icon-sm {
  width: 44rpx;
  height: 44rpx;
  display: block;
  font-size: 44rpx;
}
.icon-md {
  width: 60rpx;
  height: 60rpx;
  display: block;
  font-size: 60rpx;
}
.icon-lg {
  width: 80rpx;
  height: 80rpx;
  display: block;
  font-size: 80rpx;
}
/* 组件间距 */
.space-sm {
  height: 10rpx;
}
.space-md {
  height: 20rpx;
}
.space-lg {
  height: 30rpx;
}
.space-lg {
  height: 30rpx;
}
.space-body {
  height: 150rpx;
}
.space-safe {
  height: calc(env(safe-area-inset-bottom) / 2);
  padding-bottom: calc( env(safe-area-inset-bottom) / 2);
}
.space-footer {
  height: 30rpx;
  height: calc(30rpx + env(safe-area-inset-bottom) / 2);
  padding-bottom: calc( env(safe-area-inset-bottom) / 2);
}
.space-tabbar-footer {
  height: 100rpx;
  height: calc(100rpx + env(safe-area-inset-bottom) / 2);
  padding-bottom: calc( env(safe-area-inset-bottom) / 2);
}
.space-max-footer {
  height: 180rpx;
  height: calc(180rpx + env(safe-area-inset-bottom) / 2);
  padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}
.space {
  height: 1rpx;
  background: rgba(216, 216, 216, 0.5);
}
/* 圆角 */
.radius {
  border-radius: 5000rpx;
}
.radius-5 {
  border-radius: 5rpx;
}
.radius-10 {
  border-radius: 10rpx;
}
.radius-16 {
  border-radius: 16rpx;
}
.radius-20 {
  border-radius: 20rpx;
}
.radius-26 {
  border-radius: 26rpx;
}
.radius-34 {
  border-radius: 34rpx;
}
/* 旋转 */
.rotate-45 {
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
.rotate-90 {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
.rotate-180 {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}
.rotate-270 {
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg);
}
/* 定位 */
.rel {
  position: relative;
}
.abs {
  position: absolute;
}
.fix {
  position: fixed;
  width: 100%;
  z-index: 100;
}
.fixed-top {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 100;
}
.fixed-bottom {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
}
/* 灰度 */
.grayscale {
  -webkit-filter: grayscale(100%);
  filter: grayscale(100%);
}
/* 字体颜色 */
.icon-font-color {
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
/* 其他 */
.id_card_box {
  width: 206rpx !important;
  height: 124rpx !important;
}
.id_yy_box {
  width: 594rpx !important;
  height: 258rpx !important;
}
/* button样式修改 */
button {
  font-size: 30rpx;
  border-radius: 15rpx;
  line-height: 96rpx;
  padding: 0rpx;
  margin: 0rpx;
  color: #fff;
  background: transparent;
}
button::after {
  border-color: transparent;
  border-radius: 30rpx;
}
button.plain::after {
  border-color: currentColor;
}
button[type='grey'] {
  color: #999999;
  background: #EEEEEE;
}
/* 订单按钮 */
button.order {
  min-width: 140rpx;
  padding: 0 20rpx;
  line-height: 52rpx;
  color: #5B5B5B;
  background: #fff;
  border-radius: 8rpx;
  border: 1rpx solid #979797;
  -webkit-transform: rotateZ(360deg);
          transform: rotateZ(360deg);
  font-size: 26rpx;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-left: 30rpx;
}
/* 购物车按钮 */
button.reduce,
button.add {
  width: 38rpx;
  height: 38rpx;
  border-radius: 10rpx;
  border: 1rpx solid #EEEEEE;
  -webkit-transform: rotateZ(360deg);
          transform: rotateZ(360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
button.reduce .iconfont,
button.add .iconfont {
  font-size: 24rpx;
  line-height: 24rpx;
  overflow: hidden;
}
button.addreduce,
input.addreduce {
  min-width: 60rpx;
  padding: 0 10rpx;
  height: 38rpx;
  color: #666666;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0rpx 10rpx;
}
input.addreduce {
  text-align: center;
  max-width: 150rpx;
}
/* 按钮按下 */
.button-hover {
  opacity: 0.6;
}
/* 清除按钮默认样式 */
.clear-btn {
  margin: 0;
  padding: 0;
  background: transparent;
  border-radius: 0rpx;
  line-height: 1.5;
  border: none;
  text-align: left;
  color: #333;
}
.clear-btn::after {
  border: none;
}
@font-face {
  font-family: "iconfont";
  /* Project id 2649930 */
  src: url("https://at.alicdn.com/t/c/font_2649930_0mfiuyewp0e.woff2?t=*************") format("woff2"), url("https://at.alicdn.com/t/c/font_2649930_0mfiuyewp0e.woff?t=*************") format("woff"), url("https://at.alicdn.com/t/c/font_2649930_0mfiuyewp0e.ttf?t=*************") format("truetype");
}
.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  line-height: 1;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icongerenxinxi:before {
  content: "\e616";
}
.iconzhiweixiangqing:before {
  content: "\e6e5";
}
.icon-account-line:before {
  content: "\e688";
}
.icon-person-accounts:before {
  content: "\e6ba";
}
.icon-chaifenyemian:before {
  content: "\e6c0";
}
.icon-diy:before {
  content: "\e629";
}
.icon-zuzhi:before {
  content: "\eaaa";
}
.icon-qudao:before {
  content: "\e63b";
}
.iconjishijiedan:before {
  content: "\e6b9";
}
.iconerweima3:before {
  content: "\e84c";
}
.iconerweima:before {
  content: "\e623";
}
.iconjishidaoda:before {
  content: "\e6b3";
}
.iconjishichufa:before {
  content: "\e6b5";
}
.iconjishifuwu:before {
  content: "\e6b6";
}
.iconjishiwancheng:before {
  content: "\e6bd";
}
.iconyijiedan:before {
  content: "\e6be";
}
.icondaijiedan:before {
  content: "\e6bf";
}
.iconjingbao:before {
  content: "\e636";
}
.iconanmo1:before {
  content: "\e857";
}
.iconanmo2:before {
  content: "\e858";
}
.iconchongzhi:before {
  content: "\e859";
}
.iconbodadianhua:before {
  content: "\e85a";
}
.icondaipingjia:before {
  content: "\e85b";
}
.icondizhiguanli:before {
  content: "\e85c";
}
.icondingdan2:before {
  content: "\e85d";
}
.iconfuwuzhong:before {
  content: "\e85e";
}
.iconjifen3:before {
  content: "\e860";
}
.icondingdan3:before {
  content: "\e861";
}
.iconjuli:before {
  content: "\e867";
}
.iconlianxikefu:before {
  content: "\e868";
}
.iconbangdingjishi:before {
  content: "\e869";
}
.iconpinglun:before {
  content: "\e86a";
}
.iconqiehuanjishiduan:before {
  content: "\e86b";
}
.icondaizhifu:before {
  content: "\e86c";
}
.icondaifuwu:before {
  content: "\e86d";
}
.iconshaixuanshang-1:before {
  content: "\e870";
}
.iconshijianguanli:before {
  content: "\e873";
}
.iconshipin1:before {
  content: "\e875";
}
.iconshoucang1:before {
  content: "\e876";
}
.iconshenqingjishi:before {
  content: "\e877";
}
.iconshoucangjishi:before {
  content: "\e878";
}
.iconshouye21:before {
  content: "\e879";
}
.iconshouye11:before {
  content: "\e87a";
}
.iconpingfen:before {
  content: "\e87b";
}
.icontuikuan:before {
  content: "\e87c";
}
.icontuiguanghaibao:before {
  content: "\e87d";
}
.iconwodeshouyi:before {
  content: "\e87e";
}
.iconxiangqing:before {
  content: "\e87f";
}
.iconwodetuandui1:before {
  content: "\e880";
}
.iconshaixuanxia-1:before {
  content: "\e881";
}
.iconsousuo1:before {
  content: "\e883";
}
.iconshoucang2:before {
  content: "\e884";
}
.iconwode2:before {
  content: "\e885";
}
.iconyaoqingyouli:before {
  content: "\e886";
}
.iconwode11:before {
  content: "\e887";
}
.icon-down-fill:before {
  content: "\e665";
}
.icon-up-fill:before {
  content: "\e666";
}
.iconweixin:before {
  content: "\e64f";
}
.icon-share:before {
  content: "\e638";
}
.iconnan-xiaotu:before {
  content: "\e7b1";
}
.iconnv-xiaotu:before {
  content: "\e7b4";
}
.icontongzhi:before {
  content: "\e64e";
}
.iconsanjiao_xia:before {
  content: "\e671";
}
.iconbalance:before {
  content: "\e763";
}
.iconweixinzhifu1:before {
  content: "\e764";
}
.iconqianbao:before {
  content: "\e829";
}
.iconhuiyuanka:before {
  content: "\e7af";
}
.icon-liuyanguanli:before {
  content: "\e6bb";
}
.iconguanzhu:before {
  content: "\e68c";
}
.iconguanzhuxuanzhong:before {
  content: "\e689";
}
.iconyduixingxingkongxin:before {
  content: "\e779";
}
.iconyduixingxingshixin:before {
  content: "\e77b";
}
.icon-yuyue:before {
  content: "\e6da";
}
.icon-yingxiao:before {
  content: "\e701";
}
.icon-gouwuche-fill:before {
  content: "\e600";
}
.icon-dingwei-fill:before {
  content: "\e602";
}
.icon-left:before {
  content: "\e604";
}
.icon-right:before {
  content: "\e608";
}
.icon-close:before {
  content: "\e609";
}
.icon-add:before {
  content: "\e611";
}
.icon-gouwudai:before {
  content: "\e73e";
}
.icon-caiwu:before {
  content: "\e6a7";
}
.icon-kehu:before {
  content: "\e6ad";
}
.icon-member:before {
  content: "\e761";
}
.icon-shenhe:before {
  content: "\e695";
}
.icon-xitong:before {
  content: "\e6a8";
}
.icon-daifukuan:before {
  content: "\e787";
}
.icon-dingdanguanli:before {
  content: "\e632";
}
.icon-shangpin:before {
  content: "\e63c";
}
.icon-dianpu:before {
  content: "\e66c";
}
.icon-daihexiao:before {
  content: "\e6f8";
}
.icon-yiwancheng:before {
  content: "\e6f9";
}
.icon-pingjia:before {
  content: "\e77e";
}
.icon-home:before {
  content: "\e775";
}
.icon-home-fill:before {
  content: "\e776";
}
.icon-dingwei:before {
  content: "\e612";
}
.icon-shouhouguanli:before {
  content: "\e773";
}
.icon-jishi:before {
  content: "\e652";
}
.icon-wodeshouhou:before {
  content: "\e60d";
}
.icon-tongzhi-fill:before {
  content: "\e615";
}
.icon-jishi-fill:before {
  content: "\e601";
}
.icon-tuichu:before {
  content: "\e603";
}
.icon-camera:before {
  content: "\e60e";
}
.icon-down:before {
  content: "\e60a";
}
.icon-xuanze:before {
  content: "\e77c";
}
.icon-xuanze-fill:before {
  content: "\e77d";
}
.icon-switch:before {
  content: "\e642";
}
.icon-switch-on:before {
  content: "\e643";
}
.icon-jian-fill:before {
  content: "\e61f";
}
.icon-jian:before {
  content: "\e6fa";
}
.icon-radio-fill:before {
  content: "\e620";
}
.icon-gouwudai-fill:before {
  content: "\e6d8";
}
.icon-eyeclose:before {
  content: "\e6ab";
}
.icon-eyeopen:before {
  content: "\e6ac";
}
.icon-mima:before {
  content: "\e69e";
}
.icon-username:before {
  content: "\e6b7";
}
.icon-mine:before {
  content: "\e6f5";
}
.icon-mine-fill:before {
  content: "\e6f6";
}
.icon-tongzhi:before {
  content: "\e60b";
}
.icon-jia-bold:before {
  content: "\e613";
}
.icon-jian-bold:before {
  content: "\e614";
}
.icon-tianjia:before {
  content: "\e653";
}
.icon-zhuanhuan:before {
  content: "\e6c2";
}
.icon-xiaochengxu:before {
  content: "\e6c3";
}
.icon-warn:before {
  content: "\e6a5";
}
.icon-weirenzheng:before {
  content: "\e63a";
}
.icon-biaoqian:before {
  content: "\e60c";
}
.icon-shuaxin:before {
  content: "\e654";
}
/*flex布局,可以自己定义适合自己的*/
.flex {
  display: flex;
}
.flex-1 {
  flex: 1;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-warp {
  display: flex;
  flex-wrap: wrap;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-x-center {
  display: flex;
  justify-content: center;
}
.flex-x-between {
  display: flex;
  justify-content: space-between;
}
.flex-y-center {
  display: flex;
  align-items: center;
}
.flex-y-start {
  display: flex;
  align-items: flex-start;
}
.flex-y-end {
  display: flex;
  align-items: flex-end;
}
.flex-y-baseline {
  display: flex;
  align-items: baseline;
}
/* ios_自定义navBar */
.back-user-ios {
  width: 87px;
  height: 32px;
  border-radius: 32px;
  margin-top: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border: 1px solid #eeeeee;
  margin-left: 24rpx;
  -webkit-transform: rotateZ(360deg);
          transform: rotateZ(360deg);
}
.back-user-ios .back-user_avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #f4f6f8;
}
.back-user-ios .back-user_text {
  font-size: 11px;
  line-height: 44px;
  margin-left: 5px;
}
/* .nav_c_text {
	line-height: 44px;
	font-size: 16px;
} */
/* android_自定义navBar */
.back-user-android {
  width: 87px;
  height: 32px;
  border-radius: 32px;
  margin-top: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border: 1px solid #eeeeee;
  margin-left: 24rpx;
  -webkit-transform: rotateZ(360deg);
          transform: rotateZ(360deg);
}
.back-user-android .back-user_avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #f4f6f8;
}
.back-user-android .back-user_text {
  font-size: 11px;
  line-height: 44px;
  margin-left: 5px;
}
.nav_c_text {
  line-height: 44px;
  font-size: 16px;
}
.common-popup-content {
  width: 620rpx;
  height: auto;
  padding: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.common-popup-content .title {
  font-size: 40rpx;
  font-family: Microsoft YaHei;
  font-weight: bold;
  color: #333;
}
.common-popup-content .desc,
.common-popup-content .name {
  font-size: 24rpx;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #666;
  margin-top: 5rpx;
}
.common-popup-content .name {
  color: #999;
  margin-top: 15rpx;
}
.common-popup-content .image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 15rpx;
  margin-top: 40rpx;
}
.common-popup-content .image.middle {
  width: 300rpx;
  height: 300rpx;
}
.common-popup-content .input {
  width: 480rpx;
  height: 110rpx;
  background: #F7F7F7;
}
.common-popup-content .textarea {
  width: 480rpx;
  height: 300rpx;
  background: #F7F7F7;
}
.common-popup-content .button {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 50rpx;
}
.common-popup-content .button .item-child {
  width: 240rpx;
  height: 90rpx;
  color: #666;
  background: #EEEEEE;
  border-radius: 45rpx;
  margin: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* 分享按钮 */
.common-share-btn {
  right: 30rpx;
  bottom: 140rpx;
  width: 90rpx;
  height: 90rpx;
  margin-bottom: calc(env(safe-area-inset-bottom) / 2);
}
.common-share-btn .iconfont {
  font-size: 40rpx;
}
.common-share-btn.detail {
  bottom: 170rpx;
}
.map-info .iconjuli {
  font-size: 42rpx;
}
.map-info .icon-down {
  -webkit-transform: scale(0.5);
          transform: scale(0.5);
}
/* 订单相关页面 */
.order-pages .address-info .address-icon {
  width: 64rpx;
  height: 64rpx;
}
.order-pages .address-info .address-icon .iconfont {
  font-size: 38rpx;
}
.order-pages .address-info .username {
  font-size: 30rpx;
}
.order-pages .item-child {
  /* .goods-title {
			max-width: 435rpx;
		} */
}
.order-pages .item-child .grayscale .c-title,
.order-pages .item-child .grayscale .c-warning {
  color: #999;
}
.order-pages .item-child .cover {
  width: 155rpx;
  height: 155rpx;
}
.order-pages .item-child .copy-btn {
  width: 60rpx;
  height: 32rpx;
  background: #EEEEEE;
  text-align: center;
}
.order-pages .item-child .refund-img {
  width: 196rpx;
  height: 196rpx;
}
.order-pages .item-child .refund-img:nth-child(3n) {
  margin-right: 0;
}
.order-pages .item-textarea {
  width: 570rpx;
  height: 300rpx;
}
.order-pages .menu-list {
  margin-top: -30rpx;
}
.order-pages .menu-list .menu-title {
  height: 90rpx;
}
.order-pages .menu-list .menu-title .iconfont {
  font-size: 24rpx;
}
.order-pages .menu-list .menu-line {
  width: 80%;
  top: 76rpx;
  left: 10%;
}
.order-pages .menu-list .item-child {
  width: 20%;
  margin: 10rpx 0;
}
.order-pages .menu-list .item-child .item-img {
  width: 72rpx;
  height: 72rpx;
  z-index: 9;
  border: 1rpx solid #666;
}
.order-pages .menu-list .item-child .item-img .iconfont {
  font-size: 40rpx;
}
.order-pages .footer-info {
  bottom: 0;
}
.order-pages .footer-info .item-btn {
  width: 150rpx;
  height: 64rpx;
  background: #EEEEEE;
}
/* 申请表单相关页面 */
.apply-pages .apply-form .item-text {
  width: 200rpx;
  height: 30rpx;
  line-height: 30rpx;
  font-size: 30rpx;
  color: #1F1F1F;
}
.apply-pages .apply-form .item-input {
  min-height: 30rpx;
  line-height: 30rpx;
  padding: 25rpx 0;
  font-size: 26rpx;
  color: #A9A9A9;
}
.apply-pages .apply-form .item-input.text {
  padding: 30rpx 0;
}
.apply-pages .apply-form .item-textarea {
  width: 630rpx;
  height: 400rpx;
  color: #A9A9A9;
}
.apply-pages .apply-form .icon-switch,
.apply-pages .apply-form .icon-switch-on {
  font-size: 90rpx;
  line-height: 46rpx;
}
/* 储值明细记录 */
.stored-record-pages .list-time {
  z-index: 99999;
}
.stored-record-pages .list-time .item-child {
  width: 50%;
  height: 95rpx;
}
.stored-record-pages .list-time .item-child .iconfont {
  font-size: 28rpx;
}
.stored-record-pages movable-area,
.stored-record-pages movable-view {
  width: 686rpx;
  height: 148rpx;
  overflow: hidden;
}
.stored-record-pages .touch-item {
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  width: 686rpx;
  height: 100%;
  overflow: hidden;
}
.stored-record-pages .content {
  width: 100%;
  transition: all 0.4s;
  -webkit-transform: translateX(270rpx);
  transform: translateX(270rpx);
  margin-left: -270rpx;
}
.stored-record-pages .delete-btn {
  width: 120rpx;
  height: 100%;
  color: #fff;
  border-radius: 0 15rpx 15rpx 0;
  -webkit-transform: translateX(270rpx);
  transform: translateX(270rpx);
  transition: all 0.4s;
}
.stored-record-pages .touch-move-active .content,
.stored-record-pages .touch-move-active .delete-btn {
  -webkit-transform: translateX(0);
  transform: translateX(0);
}
.stored-record-pages .popup-choose-time .item-child {
  width: 50%;
}
.container {
  width: 100%;
}
.f_r_c_c {
  display: flex;
  justify-content: center;
  align-items: center;
}
.f_c {
  display: flex;
  flex-direction: column;
}
.f_c_m_c {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.f_r_sa_c {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.f_r_sb_c {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.f_r_m_c {
  display: flex;
  align-items: center;
}
.border-b {
  border-bottom: 1px solid #EEEEEE;
}
page {
  font-size: 28rpx;
  color: #222;
  line-height: 1.5;
  background: #fff;
  font-family: 'MyFont';
}
input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #A9A9A9;
}
input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #A9A9A9;
}
input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #A9A9A9;
}
input:-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: #A9A9A9;
}
view {
  box-sizing: border-box;
}
image {
  display: block;
}
/*隐藏滚动条*/
::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}

