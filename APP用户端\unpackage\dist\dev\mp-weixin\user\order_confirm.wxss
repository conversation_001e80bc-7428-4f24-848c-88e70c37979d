@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-10e95c19 {
  min-height: 100vh;
  position: relative;
  padding-bottom: 200rpx;
}
.header.data-v-10e95c19 {
  width: 750rpx;
  height: 376rpx;
  position: absolute;
  top: -300rpx;
  left: 0;
  z-index: -999;
}
.header image.data-v-10e95c19 {
  width: 100%;
  height: 100%;
}
.content.data-v-10e95c19 {
  margin-top: 10rpx;
}
.card.data-v-10e95c19 {
  margin-left: 32rpx;
  width: 686rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.16);
  border-radius: 16rpx;
  padding: 40rpx;
}
.card .top.data-v-10e95c19 {
  padding-bottom: 40rpx;
  border-bottom: 2rpx solid #F2F3F6;
}
.card .top .title.data-v-10e95c19 {
  font-size: 36rpx;
  font-weight: 500;
  color: #171717;
  letter-spacing: 2rpx;
}
.card .top .price.data-v-10e95c19 {
  margin-top: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #E72427;
}
.card .bottom.data-v-10e95c19 {
  padding-top: 24rpx;
  display: flex;
}
.card .bottom .left.data-v-10e95c19 {
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  padding-top: 10rpx;
}
.card .bottom .right.data-v-10e95c19 {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.card .bottom .right .tag.data-v-10e95c19 {
  width: -webkit-fit-content;
  width: fit-content;
  height: 44rpx;
  padding: 0 12rpx;
  background: #DCEAFF;
  border-radius: 4rpx;
  font-size: 16rpx;
  font-weight: 400;
  color: #2E80FE;
  line-height: 44rpx;
  text-align: center;
  margin: 10rpx;
}
.chol .choose.data-v-10e95c19 {
  padding: 40rpx 32rpx;
}
.chol .choose .title.data-v-10e95c19 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}
.chol .choose .title ._span.data-v-10e95c19 {
  color: #E72427;
}
.chol .choose .input-container.data-v-10e95c19 {
  margin-top: 40rpx;
  position: relative;
  width: 100%;
  min-height: 88rpx;
}
.chol .choose .form-input.data-v-10e95c19 {
  box-sizing: border-box;
  width: 100%;
  height: 88rpx;
  background: #F7F7F7;
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  line-height: 88rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}
.chol .choose .form-input.data-v-10e95c19:focus {
  background: #fff;
  border-color: #2E80FE;
  box-shadow: 0 0 0 4rpx rgba(46, 128, 254, 0.1);
  outline: none;
}
.chol .choose .desc.data-v-10e95c19 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
}
.chol .choose .up.data-v-10e95c19 {
  margin-bottom: 40rpx;
}
.chol .choose .cho_box.data-v-10e95c19 {
  margin-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
}
.chol .choose .cho_box .box_item.data-v-10e95c19 {
  width: -webkit-fit-content;
  width: fit-content;
  padding: 0 20rpx;
  height: 60rpx;
  background: #FFFFFF;
  border-radius: 4rpx;
  border: 2rpx solid #D8D8D8;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
  line-height: 60rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.chol .choose .cho_box .box_item .ok.data-v-10e95c19 {
  width: 20rpx;
  height: 20rpx;
  position: absolute;
  right: 0;
  bottom: 0;
  background-color: #2E80FE;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chol .fg.data-v-10e95c19 {
  width: 750rpx;
  height: 20rpx;
  background: #F3F4F5;
}
.footer.data-v-10e95c19 {
  padding: 38rpx 32rpx;
  width: 750rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  transition: bottom 0.25s ease;
}
.footer .righ.data-v-10e95c19 {
  width: 690rpx;
  height: 88rpx;
  background: #2e80fe;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #ffffff;
  line-height: 88rpx;
  text-align: center;
  transition: all 0.2s ease;
}
.footer .righ.submitting.data-v-10e95c19 {
  background: #8bb8ff;
  opacity: 0.7;
  pointer-events: none;
}
/* iOS安全区域适配 */
@supports (bottom: env(safe-area-inset-bottom)) {
.footer.data-v-10e95c19 {
    padding-bottom: calc(38rpx + env(safe-area-inset-bottom));
}
}

