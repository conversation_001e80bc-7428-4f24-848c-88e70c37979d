@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.floating-contact {
  position: fixed;
  bottom: 470rpx;
  right: 30rpx;
  z-index: 1000;
  background-color: #fff;
  border-radius: 50rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
}
.contact-container {
  display: flex;
  align-items: center;
}
.contact-btn {
  background: none;
  border: none;
  color: #576b95;
  font-size: 30rpx;
  line-height: 1.5;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
}
.contact-btn:active {
  background-color: #ededee;
}
.pages-mine {
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.pages-mine .header {
  height: 292rpx;
  background-color: #599EFF;
  position: relative;
}
.pages-mine .header .header-content {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx 0;
  position: relative;
}
.pages-mine .header .header-content .avatar_view {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
}
.pages-mine .header .header-content .avatar_view .avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}
.pages-mine .header .header-content .user-info {
  margin-left: 20rpx;
  color: #fff;
}
.pages-mine .header .header-content .user-info .user-info-logged {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}
.pages-mine .header .header-content .user-info .nickname {
  font-size: 36rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 10rpx;
}
.pages-mine .header .header-content .user-info .phone-number {
  font-size: 28rpx;
  opacity: 0.9;
}
.pages-mine .header .header-content .user-info .status-badge {
  display: inline-block;
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  line-height: 1.2;
  border-radius: 20rpx;
  color: #fff;
  text-align: center;
  margin-top: 10rpx;
  width: -webkit-fit-content;
  width: fit-content;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.pages-mine .header .header-content .user-info .status-not-registered {
  background-color: #b0b0b0;
}
.pages-mine .header .header-content .user-info .status-pending {
  background-color: #f4b400;
}
.pages-mine .header .header-content .user-info .status-approved {
  background-color: #f5a623;
}
.pages-mine .header .header-content .user-info .status-rejected {
  background-color: #f44336;
}
.pages-mine .header .header-content .settings {
  position: absolute;
  right: 30rpx;
  top: 100rpx;
  color: #fff;
  font-size: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pages-mine .header .header-content .settings .icon-xitong {
  font-size: 40rpx;
  color: #fff;
}
.pages-mine .box1 {
  margin-top: -20rpx;
  border-radius: 36rpx 36rpx 0 0;
  position: relative;
  z-index: 10;
}
.pages-mine .mine-menu-list {
  background-color: #fff;
  margin: 0 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.pages-mine .mine-menu-list .menu-title {
  height: 90rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx 0 40rpx;
  border-bottom: 1px solid #f0f0f0;
}
.pages-mine .mine-menu-list .menu-title .f-paragraph {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.pages-mine .mine-menu-list .flex-warp {
  display: flex;
  flex-wrap: wrap;
  padding: 10rpx 0;
}
.pages-mine .mine-menu-list .flex-warp .order-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33.3%;
  padding-bottom: 10rpx;
  margin-top: 15rpx;
  font-size: 25rpx;
  color: #666;
  transition: -webkit-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s;
}
.pages-mine .mine-menu-list .flex-warp .order-item:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.pages-mine .mine-menu-list .flex-warp .order-item .icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pages-mine .mine-menu-list .flex-warp .order-item .number-circle {
  position: absolute;
  top: -10rpx;
  right: -5rpx;
  width: 30rpx;
  height: 30rpx;
  background-color: #ff4d4f;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}
.pages-mine .mine-menu-list .flex-warp .mt-sm {
  margin-top: 16rpx;
}
.pages-mine .spacer {
  height: 20rpx;
  background-color: transparent;
}
.pages-mine .mine-tool-grid {
  background-color: #fff;
  margin: 0 20rpx 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
}
.pages-mine .mine-tool-grid .grid-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 20rpx;
}
.pages-mine .mine-tool-grid .grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(33.33% - 20rpx);
  min-width: 140rpx;
  transition: -webkit-transform 0.2s ease;
  transition: transform 0.2s ease;
  transition: transform 0.2s ease, -webkit-transform 0.2s ease;
}
.pages-mine .mine-tool-grid .grid-item:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.pages-mine .mine-tool-grid .grid-item .grid-icon-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.pages-mine .mine-tool-grid .grid-item .grid-icon-container.switch-identity {
  /* Specific styling for switch-identity icon */
}
.pages-mine .mine-tool-grid .grid-item .grid-text {
  font-size: 25rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  margin-bottom: 8rpx;
}
.pages-mine .mine-tool-grid .grid-item .grid-subtitle {
  font-size: 24rpx;
  color: #A1A1A1;
  text-align: center;
}
.pages-mine .flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

