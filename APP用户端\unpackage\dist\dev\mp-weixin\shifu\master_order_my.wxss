@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-ba51cf60 {
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f7fa 0%, #e4e7ed 100%);
  padding: 32rpx 24rpx;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}
.box.data-v-ba51cf60 {
  margin: 0 auto;
  max-width: 690rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 32rpx;
}
/* Title Styling */
.title.data-v-ba51cf60 {
  display: flex;
  align-items: center;
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 24rpx;
}
.title .title-icon.data-v-ba51cf60 {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}
/* Info Box Styling */
.info-box.data-v-ba51cf60 {
  background: #fafafa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}
.info-item.data-v-ba51cf60 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #e5e7eb;
}
.info-item.data-v-ba51cf60:last-child {
  border-bottom: none;
}
.info-item .label.data-v-ba51cf60 {
  font-size: 28rpx;
  font-weight: 500;
  color: #6b7280;
}
.info-item .value.data-v-ba51cf60 {
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  max-width: 420rpx;
  white-space: normal;
  word-break: break-all;
  line-height: 1.4;
}
.info-item .value.align-right.data-v-ba51cf60 {
  text-align: right;
}
.navigation-btn.data-v-ba51cf60 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 180rpx;
  height: 64rpx;
  background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
  border-radius: 32rpx;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 24rpx;
  margin-left: auto;
}
.navigation-btn .nav-icon.data-v-ba51cf60 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}
/* Dynamic Section Styling */
.dynamic-section.data-v-ba51cf60 {
  margin-bottom: 32rpx;
}
.img-box.data-v-ba51cf60 {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 24rpx;
}
.dynamic-image.data-v-ba51cf60 {
  width: 196rpx;
  height: 196rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e5e7eb;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
/* Image Modal Styling */
.image-modal.data-v-ba51cf60 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content.data-v-ba51cf60 {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
  height: 100vh;
  position: relative;
}
.modal-image.data-v-ba51cf60 {
  width: 100vw;
  height: 100vh;
  object-fit: contain;
  /* Ensures image scales to fit without distortion */
  border-radius: 0;
  /* Remove border-radius for full-screen effect */
}
.close-btn.data-v-ba51cf60 {
  position: absolute;
  bottom: 20rpx;
  padding: 16rpx 32rpx;
  background: #ffffff;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 500;
  z-index: 1100;
  /* Ensure button is above image */
}
/* Text and Notes Styling */
.text-box.data-v-ba51cf60 {
  margin-top: 24rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  color: #4b5563;
}
.notes-box.data-v-ba51cf60 {
  margin-top: 24rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  color: #374151;
  line-height: 1.5;
  word-break: break-all;
}

