<view class="login-container data-v-18804380"><view class="bg-decoration data-v-18804380"><view class="circle circle-1 data-v-18804380"></view><view class="circle circle-2 data-v-18804380"></view><view class="circle circle-3 data-v-18804380"></view></view><view class="header data-v-18804380"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="back-btn data-v-18804380" bindtap="__e"><u-icon vue-id="f7872a74-1" name="arrow-left" color="#3b82f6" size="20" class="data-v-18804380" bind:__l="__l"></u-icon></view></view><view class="logo-section data-v-18804380"><view class="logo-wrapper data-v-18804380"><image class="logo data-v-18804380" src="/static/images/logo-index.jpg" mode="aspectFit"></image></view><view class="app-name data-v-18804380">今师傅</view><view class="welcome-text data-v-18804380">{{$root.m0}}</view></view><view class="main-card data-v-18804380"><block wx:if="{{currentMode==='login'}}"><view class="data-v-18804380"><view class="tab-switcher data-v-18804380"><view data-event-opts="{{[['tap',[['switchLoginType',['password']]]]]}}" class="{{['tab-item','data-v-18804380',(loginType==='password')?'active':'']}}" bindtap="__e"><u-icon vue-id="f7872a74-2" name="lock" size="16" color="{{loginType==='password'?'#3b82f6':'#94a3b8'}}" class="data-v-18804380" bind:__l="__l"></u-icon><text class="data-v-18804380">密码登录</text></view><view data-event-opts="{{[['tap',[['switchLoginType',['sms']]]]]}}" class="{{['tab-item','data-v-18804380',(loginType==='sms')?'active':'']}}" bindtap="__e"><u-icon vue-id="f7872a74-3" name="chat" size="16" color="{{loginType==='sms'?'#3b82f6':'#94a3b8'}}" class="data-v-18804380" bind:__l="__l"></u-icon><text class="data-v-18804380">验证码登录</text></view></view><block wx:if="{{loginType==='password'}}"><view class="form-content data-v-18804380"><view class="input-group data-v-18804380"><view class="input-item data-v-18804380"><view class="input-icon data-v-18804380"><u-icon vue-id="f7872a74-4" name="phone" color="#3b82f6" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view><input class="input-field data-v-18804380" type="number" placeholder="请输入手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['loginForm']]]]]}}" value="{{loginForm.phone}}" bindinput="__e"/></view><view class="input-item data-v-18804380"><view class="input-icon data-v-18804380"><u-icon vue-id="f7872a74-5" name="lock" color="#3b82f6" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view><input class="input-field data-v-18804380" type="{{showPassword?'text':'password'}}" placeholder="请输入密码" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['loginForm']]]]]}}" value="{{loginForm.password}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['togglePassword',['$event']]]]]}}" class="action-icon data-v-18804380" bindtap="__e"><u-icon vue-id="f7872a74-6" name="{{showPassword?'eye':'eye-off'}}" color="#94a3b8" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view></view></view><view class="login-links data-v-18804380"><view data-event-opts="{{[['tap',[['switchMode',['forgot']]]]]}}" class="forgot-link data-v-18804380" bindtap="__e">忘记密码？</view><view data-event-opts="{{[['tap',[['switchMode',['register']]]]]}}" class="register-link data-v-18804380" bindtap="__e">还没有账号？<text class="link-highlight data-v-18804380">立即注册</text></view></view></view></block><block wx:if="{{loginType==='sms'}}"><view class="form-content data-v-18804380"><view class="input-group data-v-18804380"><view class="input-item data-v-18804380"><view class="input-icon data-v-18804380"><u-icon vue-id="f7872a74-7" name="phone" color="#3b82f6" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view><input class="input-field data-v-18804380" type="number" placeholder="请输入手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['smsForm']]]]]}}" value="{{smsForm.phone}}" bindinput="__e"/></view><view class="input-item data-v-18804380"><view class="input-icon data-v-18804380"><u-icon vue-id="f7872a74-8" name="chat" color="#3b82f6" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view><input class="input-field data-v-18804380" type="number" placeholder="请输入验证码" maxlength="6" data-event-opts="{{[['input',[['__set_model',['$0','code','$event',[]],['smsForm']]]]]}}" value="{{smsForm.code}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['sendSmsCode',['$event']]]]]}}" class="{{['sms-btn','data-v-18804380',(smsCountdown>0)?'disabled':'']}}" bindtap="__e">{{''+(smsCountdown>0?smsCountdown+'s':'获取验证码')+''}}</view></view></view></view></block></view></block><block wx:if="{{currentMode==='register'}}"><view class="form-content data-v-18804380"><view class="input-group data-v-18804380"><view class="input-item data-v-18804380"><view class="input-icon data-v-18804380"><u-icon vue-id="f7872a74-9" name="phone" color="#3b82f6" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view><view class="input-content data-v-18804380"><view class="input-label data-v-18804380">手机号</view><input class="input-field data-v-18804380" type="number" placeholder="请输入11位手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['registerForm']]]]]}}" value="{{registerForm.phone}}" bindinput="__e"/></view><block wx:if="{{$root.m1}}"><view class="input-status data-v-18804380"><u-icon vue-id="f7872a74-10" name="checkmark-circle" color="#52c41a" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view></block></view><view class="input-item data-v-18804380"><view class="input-icon data-v-18804380"><u-icon vue-id="f7872a74-11" name="lock" color="#3b82f6" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view><view class="input-content data-v-18804380"><view class="input-label data-v-18804380">设置密码</view><input class="input-field data-v-18804380" type="{{showPassword?'text':'password'}}" placeholder="请设置6-20位密码" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['registerForm']]]]]}}" value="{{registerForm.password}}" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['togglePassword',['$event']]]]]}}" class="action-icon data-v-18804380" bindtap="__e"><u-icon vue-id="f7872a74-12" name="{{showPassword?'eye':'eye-off'}}" color="#94a3b8" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view></view><block wx:if="{{registerForm.password}}"><view class="password-strength data-v-18804380"><view class="strength-label data-v-18804380">密码强度：</view><view class="strength-bar data-v-18804380"><view class="{{['strength-item','data-v-18804380',($root.m2>=1)?'active':'']}}"></view><view class="{{['strength-item','data-v-18804380',($root.m3>=2)?'active':'']}}"></view><view class="{{['strength-item','data-v-18804380',($root.m4>=3)?'active':'']}}"></view></view><view class="strength-text data-v-18804380">{{$root.m5}}</view></view></block><view class="input-item data-v-18804380"><view class="input-icon data-v-18804380"><u-icon vue-id="f7872a74-13" name="chat" color="#3b82f6" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view><view class="input-content data-v-18804380"><view class="input-label data-v-18804380">短信验证码</view><input class="input-field data-v-18804380" type="number" placeholder="请输入6位验证码" maxlength="6" data-event-opts="{{[['input',[['__set_model',['$0','shortCode','$event',[]],['registerForm']]]]]}}" value="{{registerForm.shortCode}}" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['sendSmsCode',['$event']]]]]}}" class="{{['sms-btn','data-v-18804380',(smsCountdown>0)?'disabled':'']}}" bindtap="__e">{{''+(smsCountdown>0?smsCountdown+'s':'获取验证码')+''}}</view></view><view class="input-item optional data-v-18804380"><view class="input-icon data-v-18804380"><u-icon vue-id="f7872a74-14" name="gift" color="#3b82f6" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view><view class="input-content data-v-18804380"><view class="input-label data-v-18804380">邀请码<text class="optional-tag data-v-18804380">选填</text></view><input class="input-field data-v-18804380" type="text" placeholder="填写邀请码可获得奖励" data-event-opts="{{[['input',[['__set_model',['$0','pidInviteCode','$event',[]],['registerForm']]]]]}}" value="{{registerForm.pidInviteCode}}" bindinput="__e"/></view><block wx:if="{{registerForm.pidInviteCode}}"><view class="input-benefit data-v-18804380"><u-icon vue-id="f7872a74-15" name="gift" color="#ff9500" size="16" class="data-v-18804380" bind:__l="__l"></u-icon></view></block></view></view></view></block><block wx:if="{{currentMode==='forgot'}}"><view class="form-content data-v-18804380"><view class="input-group data-v-18804380"><view class="input-item data-v-18804380"><view class="input-icon data-v-18804380"><u-icon vue-id="f7872a74-16" name="phone" color="#3b82f6" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view><input class="input-field data-v-18804380" type="number" placeholder="请输入手机号" maxlength="11" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['forgotForm']]]]]}}" value="{{forgotForm.phone}}" bindinput="__e"/></view><view class="input-item data-v-18804380"><view class="input-icon data-v-18804380"><u-icon vue-id="f7872a74-17" name="lock" color="#3b82f6" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view><input class="input-field data-v-18804380" type="{{showPassword?'text':'password'}}" placeholder="请输入新密码" data-event-opts="{{[['input',[['__set_model',['$0','newPassword','$event',[]],['forgotForm']]]]]}}" value="{{forgotForm.newPassword}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['togglePassword',['$event']]]]]}}" class="action-icon data-v-18804380" bindtap="__e"><u-icon vue-id="f7872a74-18" name="{{showPassword?'eye':'eye-off'}}" color="#94a3b8" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view></view><view class="input-item data-v-18804380"><view class="input-icon data-v-18804380"><u-icon vue-id="f7872a74-19" name="lock" color="#3b82f6" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view><input class="input-field data-v-18804380" type="{{showConfirmPassword?'text':'password'}}" placeholder="请确认新密码" data-event-opts="{{[['input',[['__set_model',['$0','confirmPassword','$event',[]],['forgotForm']]]]]}}" value="{{forgotForm.confirmPassword}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['toggleConfirmPassword',['$event']]]]]}}" class="action-icon data-v-18804380" bindtap="__e"><u-icon vue-id="f7872a74-20" name="{{showConfirmPassword?'eye':'eye-off'}}" color="#94a3b8" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view></view><view class="input-item data-v-18804380"><view class="input-icon data-v-18804380"><u-icon vue-id="f7872a74-21" name="chat" color="#3b82f6" size="18" class="data-v-18804380" bind:__l="__l"></u-icon></view><input class="input-field data-v-18804380" type="number" placeholder="请输入验证码" maxlength="6" data-event-opts="{{[['input',[['__set_model',['$0','shortCode','$event',[]],['forgotForm']]]]]}}" value="{{forgotForm.shortCode}}" bindinput="__e"/><view data-event-opts="{{[['tap',[['sendSmsCode',['$event']]]]]}}" class="{{['sms-btn','data-v-18804380',(smsCountdown>0)?'disabled':'']}}" bindtap="__e">{{''+(smsCountdown>0?smsCountdown+'s':'获取验证码')+''}}</view></view></view></view></block><view class="agreement-section data-v-18804380"><view data-event-opts="{{[['tap',[['toggleAgreement',['$event']]]]]}}" class="checkbox-container data-v-18804380" bindtap="__e"><view class="{{['checkbox','data-v-18804380',(agreedToTerms)?'checked':'']}}"><block wx:if="{{agreedToTerms}}"><u-icon vue-id="f7872a74-22" name="checkmark" color="#fff" size="12" class="data-v-18804380" bind:__l="__l"></u-icon></block></view><view class="agreement-text data-v-18804380">我已阅读并同意<text data-event-opts="{{[['tap',[['navigateToAgreement',['service']]]]]}}" class="link data-v-18804380" catchtap="__e">《服务协议》</text>和<text data-event-opts="{{[['tap',[['navigateToAgreement',['privacy']]]]]}}" class="link data-v-18804380" catchtap="__e">《隐私政策》</text></view></view></view><view data-event-opts="{{[['tap',[['handleSubmit',['$event']]]]]}}" class="{{['action-button','data-v-18804380',(isLoading)?'disabled':'']}}" bindtap="__e"><block wx:if="{{isLoading}}"><view class="loading-icon data-v-18804380"></view></block><text class="data-v-18804380">{{isLoading?'处理中...':$root.m6}}</text></view><block wx:if="{{currentMode==='login'}}"><view data-event-opts="{{[['tap',[['handleWechatLogin',['$event']]]]]}}" class="{{['wechat-login-button','data-v-18804380',(isWechatLoading)?'disabled':'']}}" bindtap="__e"><block wx:if="{{isWechatLoading}}"><view class="loading-icon data-v-18804380"></view></block><text class="data-v-18804380">{{isWechatLoading?'微信登录中...':'微信登录'}}</text></view></block><view class="switch-links data-v-18804380"><block wx:if="{{currentMode==='register'}}"><view data-event-opts="{{[['tap',[['switchMode',['login']]]]]}}" class="link-text data-v-18804380" bindtap="__e">已有账号？<text class="link-highlight data-v-18804380">立即登录</text></view></block><block wx:if="{{currentMode==='forgot'}}"><view data-event-opts="{{[['tap',[['switchMode',['login']]]]]}}" class="link-text data-v-18804380" bindtap="__e"><text class="link-highlight data-v-18804380">返回登录</text></view></block></view></view><view class="bottom-decoration data-v-18804380"><view class="wave data-v-18804380"></view></view></view>