@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.agreement-container.data-v-1bea9d33 {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 20rpx;
}
.content-wrapper.data-v-1bea9d33 {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
.agreement-content.data-v-1bea9d33 {
  line-height: 1.8;
  font-size: 28rpx;
  color: #333;
  /* 处理富文本内容样式 */
}
.agreement-content.data-v-1bea9d33 :deep(._p) {
  margin-bottom: 20rpx;
  text-indent: 2em;
}
.agreement-content.data-v-1bea9d33 :deep(._h1), .agreement-content.data-v-1bea9d33 :deep(._h2), .agreement-content.data-v-1bea9d33 :deep(._h3) {
  font-weight: bold;
  margin: 30rpx 0 20rpx;
  color: #222;
}
.agreement-content.data-v-1bea9d33 :deep(._h1) {
  font-size: 36rpx;
}
.agreement-content.data-v-1bea9d33 :deep(._h2) {
  font-size: 32rpx;
}
.agreement-content.data-v-1bea9d33 :deep(._h3) {
  font-size: 30rpx;
}
.agreement-content.data-v-1bea9d33 :deep(._ul), .agreement-content.data-v-1bea9d33 :deep(._ol) {
  padding-left: 40rpx;
  margin-bottom: 20rpx;
}
.agreement-content.data-v-1bea9d33 :deep(._li) {
  margin-bottom: 10rpx;
}

