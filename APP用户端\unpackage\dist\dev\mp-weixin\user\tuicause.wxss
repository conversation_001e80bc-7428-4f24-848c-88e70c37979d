@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-6eb50ff2 {
  padding: 40rpx 30rpx;
  background: #f8f8f8;
  height: 100vh;
}
.page .title.data-v-6eb50ff2 {
  font-size: 32rpx;
  font-weight: 400;
  color: #323232;
}
.page .inp.data-v-6eb50ff2 {
  margin-top: 30rpx;
}
.page .inp textarea.data-v-6eb50ff2 {
  box-sizing: border-box;
  width: 690rpx;
  height: 280rpx;
  background: #FFFFFF;
  padding: 36rpx 40rpx;
}
.page .btn.data-v-6eb50ff2 {
  position: fixed;
  left: 30rpx;
  bottom: 68rpx;
  width: 690rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  line-height: 98rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
}

