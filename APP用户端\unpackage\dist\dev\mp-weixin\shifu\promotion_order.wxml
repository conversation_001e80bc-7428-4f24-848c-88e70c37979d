<view class="page data-v-212470f0"><view class="tabs data-v-212470f0"><view data-event-opts="{{[['tap',[['switchTab',[1]]]]]}}" class="{{['tab_item','data-v-212470f0',(currentTab===1)?'active':'']}}" bindtap="__e">推广给用户订单</view><view data-event-opts="{{[['tap',[['switchTab',[2]]]]]}}" class="{{['tab_item','data-v-212470f0',(currentTab===2)?'active':'']}}" bindtap="__e">推广给师傅订单</view></view><block wx:for="{{filteredList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="order_item data-v-212470f0"><view class="top data-v-212470f0"><view class="top_left data-v-212470f0">{{item.createTime}}</view><block wx:if="{{item.payType==7}}"><view class="top_right data-v-212470f0">交易成功</view></block><block wx:if="{{item.payType==-1}}"><view class="top_right data-v-212470f0">已取消</view></block><block wx:if="{{item.payType==1}}"><view class="top_right data-v-212470f0">待支付</view></block><block wx:if="{{item.payType==3}}"><view class="top_right data-v-212470f0">待上门</view></block><block wx:if="{{item.payType==5}}"><view class="top_right data-v-212470f0">待服务</view></block><block wx:if="{{item.payType==6}}"><view class="top_right data-v-212470f0">服务中</view></block></view><view class="mid data-v-212470f0"><view class="mid_left data-v-212470f0">{{item.goodsName}}</view><block wx:if="{{currentTab===2}}"><view class="mid_right data-v-212470f0">{{"￥"+item.coachServicePrice}}</view></block><block wx:else><view class="mid_right data-v-212470f0">{{"￥"+item.payPrice}}</view></block></view><view class="bottom data-v-212470f0">{{"订单号："+item.orderCode}}</view><view class="blue data-v-212470f0"></view></view></block><u-loadmore vue-id="0368ab04-1" status="{{status}}" class="data-v-212470f0" bind:__l="__l"></u-loadmore></view>