@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-f26050c2 {
  padding-bottom: 200rpx;
}
.page.data-v-f26050c2  .u-popup__content {
  display: none;
}
.page.data-v-f26050c2  .u-number-box__plus {
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx !important;
  background-color: #fff !important;
  border: 1px solid #000;
}
.page.data-v-f26050c2  .u-number-box__plus text {
  font-size: 24rpx !important;
  line-height: 36rpx !important;
}
.page.data-v-f26050c2  .u-number-box__minus {
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx !important;
  background-color: #fff !important;
  border: 1px solid #000;
}
.page.data-v-f26050c2  .u-number-box__minus text {
  font-size: 24rpx !important;
  line-height: 36rpx !important;
}
.page.data-v-f26050c2  .u-number-box__minus--disabled {
  border: 1px solid #ADADAD;
}
.page.data-v-f26050c2  .u-number-box__input {
  background-color: #fff !important;
}
.page .choose_yh.data-v-f26050c2 {
  padding-top: 40rpx;
  width: 750rpx;
  height: 1106rpx;
  background: #FFFFFF;
  border-radius: 20rpx 20rpx 0rpx 0rpx;
  opacity: 1;
  position: fixed;
  bottom: 0;
  z-index: 10088;
  transition: all 0.5s;
}
.page .choose_yh .head.data-v-f26050c2 {
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
  text-align: center;
  margin-bottom: 44rpx;
}
.page .choose_yh .close.data-v-f26050c2 {
  position: absolute;
  top: 44rpx;
  right: 32rpx;
}
.page .choose_yh .close image.data-v-f26050c2 {
  width: 37rpx;
  height: 37rpx;
}
.page .choose_yh .cou_item.data-v-f26050c2 {
  margin: 0 auto;
  width: 690rpx;
  height: 202rpx;
  background: #DCEAFF;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #2E80FE;
}
.page .choose_yh .cou_item .top.data-v-f26050c2 {
  height: 150rpx;
  display: flex;
  align-items: center;
  padding-top: 26rpx;
  padding-left: 24rpx;
  padding-right: 14rpx;
  position: relative;
  border-bottom: 2rpx dashed #2E80FE;
}
.page .choose_yh .cou_item .top .box1.data-v-f26050c2 {
  text-align: center;
  width: 180rpx;
  font-size: 40rpx;
  font-weight: 500;
  color: #E72427;
}
.page .choose_yh .cou_item .top .box1 ._span.data-v-f26050c2 {
  font-size: 20rpx;
}
.page .choose_yh .cou_item .top .box2.data-v-f26050c2 {
  margin-left: 28rpx;
}
.page .choose_yh .cou_item .top .box2 text.data-v-f26050c2 {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
  max-width: 450rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .choose_yh .cou_item .top .box2 ._span.data-v-f26050c2 {
  margin-top: 10rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #B2B2B2;
}
.page .choose_yh .cou_item .top .box3.data-v-f26050c2 {
  position: absolute;
  right: 22rpx;
  top: 40rpx;
  width: 40rpx;
  height: 40rpx;
  background: #fff;
  border: 2rpx solid #B2B2B2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .choose_yh .cou_item .bottom.data-v-f26050c2 {
  padding: 0 24rpx;
  height: 50rpx;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  line-height: 50rpx;
  font-size: 20rpx;
  font-weight: 400;
  color: #B2B2B2;
}
.page .choose_yh .noYh.data-v-f26050c2 {
  width: 690rpx;
  margin: 0 auto;
  margin-top: 52rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 22rpx;
}
.page .choose_yh .noYh .left.data-v-f26050c2 {
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .choose_yh .noYh .right.data-v-f26050c2 {
  width: 40rpx;
  height: 40rpx;
  background: #fff;
  border: 2rpx solid #B2B2B2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .choose_yh .notcan.data-v-f26050c2 {
  margin-top: 52rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #B2B2B2;
  padding: 0 30rpx;
}
.page .footer.data-v-f26050c2 {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 202rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  background-color: #fff;
}
.page .footer .left.data-v-f26050c2 {
  font-size: 40rpx;
  font-weight: 600;
  color: #E72427;
}
.page .footer .mid.data-v-f26050c2 {
  width: -webkit-fit-content;
  width: fit-content;
  height: 98rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  line-height: 98rpx;
  text-align: center;
  font-weight: 700;
  padding: 0 15rpx;
}
.page .footer .right.data-v-f26050c2 {
  width: 294rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  opacity: 1;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 98rpx;
  text-align: center;
}
.page .fg.data-v-f26050c2 {
  height: 20rpx;
  background-color: #F3F4F5;
}
.page .address.data-v-f26050c2 {
  height: 164rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}
.page .address .left .top.data-v-f26050c2 {
  display: flex;
  align-items: center;
}
.page .address .left .top image.data-v-f26050c2 {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
}
.page .address .left .top text.data-v-f26050c2 {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
  max-width: 400rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .address .left .bottom.data-v-f26050c2 {
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
  padding-left: 56rpx;
  margin-top: 12rpx;
}
.page .main.data-v-f26050c2 {
  padding: 40rpx 32rpx;
  position: relative;
  padding-bottom: 70rpx;
}
.page .main .expand.data-v-f26050c2 {
  width: 690rpx;
  margin: 0 auto;
  font-size: 28rpx;
  font-weight: 400;
  color: #ADADAD;
  text-align: center;
  position: absolute;
  bottom: 0;
}
.page .main .expand .icon_box.data-v-f26050c2 {
  display: flex;
  justify-content: center;
}
.page .main .main_item.data-v-f26050c2 {
  display: flex;
}
.page .main .main_item image.data-v-f26050c2 {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
}
.page .main .main_item .right.data-v-f26050c2 {
  flex: 1;
}
.page .main .main_item .right .title.data-v-f26050c2 {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
  max-width: 450rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .right .price.data-v-f26050c2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 80rpx;
}
.page .main .main_item .right .price text.data-v-f26050c2 {
  font-size: 28rpx;
  font-weight: 500;
  color: #2E80FE;
}
.page .notes.data-v-f26050c2 {
  padding: 40rpx 32rpx;
}
.page .notes .title.data-v-f26050c2 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}
.page .notes textarea.data-v-f26050c2 {
  margin-top: 40rpx;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  width: 686rpx;
  height: 242rpx;
  background: #F7F7F7;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
}
.page .preferential.data-v-f26050c2 {
  display: flex;
  justify-content: space-between;
  padding: 40rpx 32rpx;
  align-items: center;
}
.page .preferential .left.data-v-f26050c2 {
  font-size: 24rpx;
  font-weight: 400;
  color: #333333;
}
.page .preferential .right.data-v-f26050c2 {
  display: flex;
  align-items: center;
}
.page .preferential .right text.data-v-f26050c2 {
  font-size: 24rpx;
  font-weight: 400;
  color: #E72427;
}

