@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
view.data-v-515940d7, scroll-view.data-v-515940d7, swiper-item.data-v-515940d7 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-notice.data-v-515940d7 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.u-notice__left-icon.data-v-515940d7 {
  align-items: center;
  margin-right: 5px;
}
.u-notice__right-icon.data-v-515940d7 {
  margin-left: 5px;
  align-items: center;
}
.u-notice__swiper.data-v-515940d7 {
  height: 16px;
  display: flex;
  flex-direction: row;
  align-items: center;
  flex: 1;
}
.u-notice__swiper__item.data-v-515940d7 {
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;
}
.u-notice__swiper__item__text.data-v-515940d7 {
  font-size: 14px;
  color: #f9ae3d;
}

