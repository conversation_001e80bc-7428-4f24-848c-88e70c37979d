@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-342423c4 {
  background-color: #f0f2f5;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.header-stats.data-v-342423c4 {
  background: linear-gradient(135deg, #2e80fe 0%, #1e6bff 100%);
  padding: 60rpx 30rpx 80rpx;
  border-bottom-left-radius: 40rpx;
  border-bottom-right-radius: 40rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}
.header-stats .stats-card.data-v-342423c4 {
  text-align: center;
  color: #ffffff;
}
.header-stats .stats-card .stats-title.data-v-342423c4 {
  font-size: 30rpx;
  opacity: 0.95;
  margin-bottom: 24rpx;
  font-weight: 400;
}
.header-stats .stats-card .stats-amount.data-v-342423c4 {
  font-size: 56rpx;
  font-weight: bold;
  letter-spacing: 1rpx;
}
.filter-section.data-v-342423c4 {
  background: #ffffff;
  margin: -40rpx 30rpx 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  padding: 24rpx 0;
  /* Remove position: relative and overflow: hidden if u-scroll-list handles its own clipping/gradients */
  /* If using u-scroll-list, the .filter-tabs-container and its scrollbar hiding are handled by u-scroll-list internally.
	   You might still need to style the inner items. */
  /* Remove these custom gradient overlays if using a dedicated ScrollList component */
  /*
	&::before,
	&::after {
		content: '';
		position: absolute;
		top: 0;
		bottom: 0;
		width: 60rpx;
		pointer-events: none;
		z-index: 1;
	}

	&::before {
		left: 0;
		background: linear-gradient(to right, #ffffff 30%, rgba(255, 255, 255, 0) 100%);
	}

	&::after {
		right: 0;
		background: linear-gradient(to left, #ffffff 30%, rgba(255, 255, 255, 0) 100%);
	}
	*/
  /* Styling for the individual tabs within the scroll list */
}
.filter-section .filter-tab.data-v-342423c4 {
  padding: 18rpx 30rpx;
  font-size: 28rpx;
  color: #666666;
  border-radius: 24rpx;
  transition: all 0.3s ease;
  text-align: center;
  flex-shrink: 0;
  /* Prevent tabs from shrinking */
  margin: 0 10rpx;
  /* Add margin between tabs for better spacing, adjust as needed */
}
.filter-section .filter-tab.data-v-342423c4:first-child {
  margin-left: 30rpx;
  /* Align first tab with filter-section padding */
}
.filter-section .filter-tab.data-v-342423c4:last-child {
  margin-right: 30rpx;
  /* Align last tab with filter-section padding */
}
.filter-section .filter-tab.active.data-v-342423c4 {
  color: #2e80fe;
  background: rgba(46, 128, 254, 0.15);
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(46, 128, 254, 0.2);
}
.record-list.data-v-342423c4 {
  padding: 0 30rpx;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* Centers each record-item horizontally */
}
.record-list .record-item.data-v-342423c4 {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.07);
  transition: -webkit-transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
  width: 100%;
  /* Take full width within padding */
  max-width: 700rpx;
  /* Optional: Constrain max width for large screens */
}
.record-list .record-item.data-v-342423c4:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
}
.record-list .record-item .record-header.data-v-342423c4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.record-list .record-item .record-header .record-amount.data-v-342423c4 {
  font-size: 38rpx;
  font-weight: bold;
  color: #333333;
}
.record-list .record-item .record-header .record-status.data-v-342423c4 {
  padding: 10rpx 20rpx;
  border-radius: 16rpx;
  font-size: 26rpx;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  white-space: nowrap;
}
.record-list .record-item .record-header .record-status.status-rejected.data-v-342423c4 {
  background: #fde0e0;
  color: #d32f2f;
}
.record-list .record-item .record-header .record-status.status-pending.data-v-342423c4 {
  background: #fff8e1;
  color: #f57c00;
}
.record-list .record-item .record-header .record-status.status-actionable.data-v-342423c4 {
  background: #e1f5fe;
  color: #039be5;
}
.record-list .record-item .record-header .record-status.status-actionable.data-v-342423c4:active {
  background: #bbdefb;
}
.record-list .record-item .record-header .record-status.status-actionable .tap-hint.data-v-342423c4 {
  font-size: 22rpx;
  margin-left: 10rpx;
  opacity: 0.85;
  color: inherit;
}
.record-list .record-item .record-header .record-status.status-pending-action.data-v-342423c4 {
  background: #fff8e1;
  color: #f57c00;
}
.record-list .record-item .record-header .record-status.status-pending-action.data-v-342423c4:active {
  background: #ffe0b2;
}
.record-list .record-item .record-header .record-status.status-pending-action .tap-hint.data-v-342423c4 {
  font-size: 22rpx;
  margin-left: 10rpx;
  opacity: 0.85;
  color: inherit;
}
.record-list .record-item .record-header .record-status.status-processing.data-v-342423c4 {
  background: #e3f2fd;
  color: #2196f3;
}
.record-list .record-item .record-header .record-status.status-success.data-v-342423c4 {
  background: #e8f5e8;
  color: #4caf50;
}
.record-list .record-item .record-header .record-status.status-failed.data-v-342423c4 {
  background: #ffebee;
  color: #f44336;
}
.record-list .record-item .record-header .record-status.status-closed.data-v-342423c4 {
  background: #f5f5f5;
  color: #999999;
}
.record-list .record-item .record-header .record-status.status-default.data-v-342423c4 {
  background: #f0f0f0;
  color: #666666;
}
.record-list .record-item .record-info .record-time.data-v-342423c4 {
  font-size: 26rpx;
  color: #888888;
  margin-bottom: 8rpx;
}
.record-list .record-item .record-info .record-method.data-v-342423c4 {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 0;
}
.empty-state.data-v-342423c4 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 150rpx 0;
  flex-grow: 1;
}
.empty-state .empty-image.data-v-342423c4 {
  width: 240rpx;
  height: 240rpx;
  margin-bottom: 40rpx;
  opacity: 0.7;
}
.empty-state .empty-text.data-v-342423c4 {
  font-size: 30rpx;
  color: #999999;
  font-weight: 500;
  margin-bottom: 400rpx;
}
.load-more.data-v-342423c4 {
  padding: 40rpx 0 60rpx;
  text-align: center;
}
.load-more .load-text.data-v-342423c4 {
  font-size: 28rpx;
  color: #999999;
}

