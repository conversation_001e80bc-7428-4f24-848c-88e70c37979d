<view class="u-index-list data-v-aecf76f2"><scroll-view style="{{'max-height:'+($root.g0)+';'}}" scrollTop="{{scrollTop}}" scrollIntoView="{{scrollIntoView}}" offset-accuracy="{{1}}" scroll-y="{{true}}" data-ref="uList" data-event-opts="{{[['scroll',[['scrollHandler',['$event']]]]]}}" bindscroll="__e" class="data-v-aecf76f2 vue-ref"><block wx:if="{{$slots.header}}"><view class="data-v-aecf76f2"><slot name="header"></slot></view></block><slot></slot><block wx:if="{{$slots.footer}}"><view class="data-v-aecf76f2"><slot name="footer"></slot></view></block></scroll-view><view data-ref="u-index-list__letter" data-event-opts="{{[['touchstart',[['touchStart',['$event']]]],['touchmove',[['touchMove',['$event']]]],['touchend',[['touchEnd',['$event']]]],['touchcancel',[['touchEnd',['$event']]]]]}}" class="u-index-list__letter data-v-aecf76f2 vue-ref" style="{{'top:'+($root.g1)+';'}}" bindtouchstart="__e" catchtouchmove="__e" catchtouchend="__e" catchtouchcancel="__e"><block wx:for="{{uIndexList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-index-list__letter__item data-v-aecf76f2" style="{{'background-color:'+(activeIndex===index?activeColor:'transparent')+';'}}"><text class="u-index-list__letter__item__index data-v-aecf76f2" style="{{'color:'+(activeIndex===index?'#fff':inactiveColor)+';'}}">{{item}}</text></view></block></view><u-transition vue-id="26eecfad-1" mode="fade" show="{{touching}}" customStyle="{{$root.a0}}" class="data-v-aecf76f2" bind:__l="__l" vue-slots="{{['default']}}"><view class="{{['u-index-list__indicator','data-v-aecf76f2','u-index-list__indicator--show']}}" style="{{'height:'+($root.g2)+';'+('width:'+($root.g3)+';')}}"><text class="u-index-list__indicator__text data-v-aecf76f2">{{uIndexList[activeIndex]}}</text></view></u-transition></view>