@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-1ee6bdd6 {
  background-color: #F8F8F8;
  min-height: 100vh;
  padding-top: 100rpx;
}
.page .header.data-v-1ee6bdd6 {
  position: fixed;
  top: 0;
  width: 750rpx;
  height: 100rpx;
  background: #FFFFFF;
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 99;
}
.page .header .header_item.data-v-1ee6bdd6 {
  max-width: 90rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  white-space: nowrap;
}
.page .header .header_item .blue.data-v-1ee6bdd6 {
  margin-top: 8rpx;
  width: 38rpx;
  height: 6rpx;
  background: #2E80FE;
  border-radius: 4rpx;
}
.page .main.data-v-1ee6bdd6 {
  padding: 40rpx 30rpx;
}
.page .main .main_item.data-v-1ee6bdd6 {
  width: 690rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 28rpx 36rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}
.page .main .main_item .head.data-v-1ee6bdd6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .main .main_item .head .no.data-v-1ee6bdd6 {
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .mid.data-v-1ee6bdd6 {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item .mid .lef.data-v-1ee6bdd6 {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}
.page .main .main_item .mid .lef image.data-v-1ee6bdd6 {
  width: 120rpx;
  height: 120rpx;
  flex-shrink: 0;
}
.page .main .main_item .mid .lef text.data-v-1ee6bdd6 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  margin-left: 30rpx;
  max-width: 350rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .mid .righ.data-v-1ee6bdd6 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  text-align: right;
  margin-left: 10rpx;
}
.page .main .main_item .bot.data-v-1ee6bdd6 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item .bot .qzf.data-v-1ee6bdd6,
.page .main .main_item .bot .qxdd.data-v-1ee6bdd6,
.page .main .main_item .bot .lxsf.data-v-1ee6bdd6,
.page .main .main_item .bot .qrwc.data-v-1ee6bdd6,
.page .main .main_item .bot .qpl.data-v-1ee6bdd6 {
  width: 148rpx;
  height: 48rpx;
  background: #2E80FE;
  border-radius: 50rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 48rpx;
  text-align: center;
}
.page .main .main_item .bot .qzf.data-v-1ee6bdd6,
.page .main .main_item .bot .qrwc.data-v-1ee6bdd6,
.page .main .main_item .bot .qpl.data-v-1ee6bdd6 {
  color: #fff;
}
.page .main .main_item .bot .qxdd.data-v-1ee6bdd6,
.page .main .main_item .bot .lxsf.data-v-1ee6bdd6 {
  background: #FFFFFF;
  border: 2rpx solid #2E80FE;
  color: #2E80FE;
}
.page .main .main_item .sub_orders.data-v-1ee6bdd6 {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1px solid #f0f0f0;
}
.page .main .main_item .sub_orders .sub_title.data-v-1ee6bdd6 {
  font-size: 26rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 20rpx;
}
.page .main .main_item .sub_orders .sub_item.data-v-1ee6bdd6 {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 15rpx;
}
.page .main .main_item .sub_orders .sub_item .sub_head.data-v-1ee6bdd6 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}
.page .main .main_item .sub_orders .sub_item .sub_head .sub_no.data-v-1ee6bdd6 {
  font-size: 22rpx;
  color: #666;
  max-width: 400rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .sub_orders .sub_item .sub_head .sub_status.data-v-1ee6bdd6 {
  font-size: 22rpx;
  color: #2E80FE;
  font-weight: 500;
}
.page .main .main_item .sub_orders .sub_item .sub_content.data-v-1ee6bdd6 {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info.data-v-1ee6bdd6 {
  flex: 1;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info .sub_amount.data-v-1ee6bdd6 {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info .sub_reason.data-v-1ee6bdd6 {
  font-size: 22rpx;
  color: #666;
  margin-bottom: 8rpx;
  max-width: 300rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_info .sub_time.data-v-1ee6bdd6 {
  font-size: 20rpx;
  color: #999;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_actions.data-v-1ee6bdd6 {
  flex-shrink: 0;
  display: flex;
  gap: 10rpx;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_actions .sub_qzf.data-v-1ee6bdd6 {
  width: 120rpx;
  height: 40rpx;
  background: #2E80FE;
  border-radius: 40rpx;
  font-size: 18rpx;
  font-weight: 400;
  line-height: 40rpx;
  text-align: center;
  color: #fff;
}
.page .main .main_item .sub_orders .sub_item .sub_content .sub_actions .sub_qzf.sub_reject.data-v-1ee6bdd6 {
  background: #ff6b6b;
}
.page .main .main_item_already.data-v-1ee6bdd6 {
  padding: 28rpx 36rpx;
  background-color: #fff;
  border-radius: 24rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}
.page .main .main_item_already .qxdd.data-v-1ee6bdd6 {
  width: 148rpx;
  height: 48rpx;
  background: #FFFFFF;
  border-radius: 50rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 48rpx;
  text-align: center;
  border: 2rpx solid #2E80FE;
  color: #2E80FE;
}
.page .main .main_item_already .no.data-v-1ee6bdd6 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item_already .mid.data-v-1ee6bdd6 {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item_already .mid .lef.data-v-1ee6bdd6 {
  display: flex;
  align-items: center;
  flex: 1;
  overflow: hidden;
}
.page .main .main_item_already .mid .lef image.data-v-1ee6bdd6 {
  width: 120rpx;
  height: 120rpx;
  flex-shrink: 0;
}
.page .main .main_item_already .mid .lef text.data-v-1ee6bdd6 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  margin-left: 30rpx;
  max-width: 350rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item_already .bot.data-v-1ee6bdd6 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .main .main_item_already .shifu.data-v-1ee6bdd6 {
  margin-top: 20rpx;
}
.page .main .main_item_already .shifu .shifu_item.data-v-1ee6bdd6 {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
}
.page .main .main_item_already .shifu .shifu_item image.data-v-1ee6bdd6 {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
}
.page .main .main_item_already .shifu .shifu_item text.data-v-1ee6bdd6 {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #333333;
}
.page .main .main_item_already .tips.data-v-1ee6bdd6 {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #2E80FE;
}
.page .main .main_item_already .title.data-v-1ee6bdd6 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.page .main .main_item_already .ok.data-v-1ee6bdd6 {
  font-size: 24rpx;
  color: #2E80FE;
  margin-top: 10rpx;
}
.modal-content-red.data-v-1ee6bdd6 {
  color: #F60100;
  padding: 10rpx 30rpx;
  text-align: center;
}
.modal-content-details.data-v-1ee6bdd6 {
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
}
.modal-content-details .detail-item.data-v-1ee6bdd6 {
  display: flex;
  margin-bottom: 10rpx;
}
.modal-content-details .detail-item .label.data-v-1ee6bdd6 {
  font-weight: bold;
  width: 150rpx;
  flex-shrink: 0;
}
.modal-content-details .detail-item .value.data-v-1ee6bdd6 {
  flex-grow: 1;
}

