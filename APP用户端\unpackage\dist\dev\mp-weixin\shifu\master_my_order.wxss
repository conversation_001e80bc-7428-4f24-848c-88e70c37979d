@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-b530dfe4 {
  background-color: #F8F8F8;
  height: 100vh;
  overflow: auto;
  padding-top: 100rpx;
}
.page .header.data-v-b530dfe4 {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 750rpx;
  height: 100rpx;
  background: #FFFFFF;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.page .header .header_item.data-v-b530dfe4 {
  max-width: 85rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.page .header .header_item .blue.data-v-b530dfe4 {
  margin-top: 8rpx;
  width: 38rpx;
  height: 6rpx;
  background: #2E80FE;
  border-radius: 4rpx;
}
.page .main.data-v-b530dfe4 {
  padding: 20rpx 30rpx;
  min-height: calc(100vh - 100rpx);
}
.page .main .main_item.data-v-b530dfe4 {
  width: 690rpx;
  height: 284rpx;
  background: #FFFFFF;
  border-radius: 24rpx;
  padding: 28rpx 36rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}
.page .main .main_item .head.data-v-b530dfe4 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .main .main_item .head .no.data-v-b530dfe4 {
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .mid.data-v-b530dfe4 {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item .mid .lef.data-v-b530dfe4 {
  display: flex;
  align-items: center;
}
.page .main .main_item .mid .lef image.data-v-b530dfe4 {
  width: 120rpx;
  height: 120rpx;
  flex-shrink: 0;
}
.page .main .main_item .mid .lef text.data-v-b530dfe4 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  margin-left: 30rpx;
  max-width: 350rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .mid .righ.data-v-b530dfe4 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  text-align: right;
  flex-shrink: 0;
}
.page .main .main_item .bot.data-v-b530dfe4 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item .bot .qzf.data-v-b530dfe4 {
  width: 148rpx;
  height: 48rpx;
  background: #2E80FE;
  border-radius: 50rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 48rpx;
  text-align: center;
  color: #fff;
}

