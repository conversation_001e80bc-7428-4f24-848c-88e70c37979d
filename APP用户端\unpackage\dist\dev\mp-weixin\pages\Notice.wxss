@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-1a30f8b1 {
  padding: 0 30rpx;
}
.page .notice_item.data-v-1a30f8b1 {
  height: 166rpx;
  display: flex;
  align-items: center;
  border-bottom: 2rpx solid #E9E9E9;
  padding: 40rpx 0;
}
.page .notice_item .left.data-v-1a30f8b1 {
  width: 84rpx;
  height: 84rpx;
  background: #AAB3CC;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .notice_item .right.data-v-1a30f8b1 {
  margin-left: 24rpx;
}
.page .notice_item .right .title.data-v-1a30f8b1 {
  max-width: 580rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .notice_item .right .ctx.data-v-1a30f8b1 {
  max-width: 580rpx;
  margin-top: 12rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

