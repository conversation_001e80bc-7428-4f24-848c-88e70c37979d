@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-027e28c9 {
  min-height: 100vh;
  position: relative;
  padding-bottom: 200rpx;
}
.header image.data-v-027e28c9 {
  width: 100%;
  height: 100%;
}
.content.data-v-027e28c9 {
  margin-top: 10rpx;
}
.card.data-v-027e28c9 {
  margin-left: 32rpx;
  width: 686rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.16);
  border-radius: 16rpx;
  padding: 40rpx;
}
.card .top.data-v-027e28c9 {
  padding-bottom: 40rpx;
  border-bottom: 2rpx solid #F2F3F6;
}
.card .top .title.data-v-027e28c9 {
  font-size: 36rpx;
  font-weight: 500;
  color: #171717;
  letter-spacing: 2rpx;
}
.card .top .price.data-v-027e28c9 {
  margin-top: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #E72427;
}
.card .bottom.data-v-027e28c9 {
  padding-top: 24rpx;
  display: flex;
}
.card .bottom .left.data-v-027e28c9 {
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  padding-top: 10rpx;
}
.card .bottom .right.data-v-027e28c9 {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.card .bottom .right .tag.data-v-027e28c9 {
  width: -webkit-fit-content;
  width: fit-content;
  height: 44rpx;
  padding: 0 12rpx;
  background: #DCEAFF;
  border-radius: 4rpx;
  font-size: 16rpx;
  font-weight: 400;
  color: #2E80FE;
  line-height: 44rpx;
  text-align: center;
  margin: 10rpx;
}
.chol .choose.data-v-027e28c9 {
  padding: 40rpx 32rpx;
}
.chol .choose .title.data-v-027e28c9 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}
.chol .choose .title ._span.data-v-027e28c9 {
  color: #E72427;
}
.chol .choose .input-container.data-v-027e28c9 {
  margin-top: 40rpx;
  position: relative;
  width: 100%;
  min-height: 88rpx;
}
.chol .choose .form-input.data-v-027e28c9 {
  box-sizing: border-box;
  width: 100%;
  height: 88rpx;
  background: #F7F7F7;
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  line-height: 88rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
}
.chol .choose .form-input.data-v-027e28c9:focus {
  background: #fff;
  border-color: #2E80FE;
  box-shadow: 0 0 0 4rpx rgba(46, 128, 254, 0.1);
  outline: none;
}
.chol .choose .desc.data-v-027e28c9 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
}
.chol .choose .up.data-v-027e28c9 {
  margin-bottom: 40rpx;
}
.chol .choose .cho_box.data-v-027e28c9 {
  margin-top: 20rpx;
  display: flex;
  flex-wrap: wrap;
}
.chol .choose .cho_box .box_item.data-v-027e28c9 {
  width: -webkit-fit-content;
  width: fit-content;
  padding: 0 20rpx;
  height: 60rpx;
  background: #FFFFFF;
  border-radius: 4rpx;
  border: 2rpx solid #D8D8D8;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
  line-height: 60rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}
.chol .choose .cho_box .box_item .ok.data-v-027e28c9 {
  width: 20rpx;
  height: 20rpx;
  position: absolute;
  right: 0;
  bottom: 0;
  background-color: #2E80FE;
  display: flex;
  align-items: center;
  justify-content: center;
}
.chol .fg.data-v-027e28c9 {
  width: 750rpx;
  height: 20rpx;
  background: #F3F4F5;
}
.footer.data-v-027e28c9 {
  padding: 20rpx 32rpx;
  width: 750rpx;
  background: #ffffff;
  box-shadow: 0rpx 0rpx 6rpx 2rpx rgba(193, 193, 193, 0.3);
  position: fixed;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 999;
  transition: bottom 0.25s ease;
}
.footer .footer-item.data-v-027e28c9 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 88rpx;
  transition: all 0.2s ease;
}
.footer .footer-service.data-v-027e28c9 {
  flex: 0 0 100rpx;
}
.footer .footer-service .footer-icon.data-v-027e28c9 {
  margin-bottom: 4rpx;
}
.footer .footer-service .footer-text.data-v-027e28c9 {
  font-size: 20rpx;
  color: #666;
}
.footer .footer-cart.data-v-027e28c9 {
  flex: 0 0 100rpx;
}
.footer .footer-cart .footer-icon.data-v-027e28c9 {
  margin-bottom: 4rpx;
}
.footer .footer-cart .footer-text.data-v-027e28c9 {
  font-size: 20rpx;
  color: #666;
}
.footer .footer-add-cart.data-v-027e28c9 {
  flex: 1;
  margin: 0 16rpx;
  background: transparent;
  border: 2rpx solid #2e80fe;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #2e80fe;
  text-align: center;
  line-height: 84rpx;
}
.footer .footer-add-cart.data-v-027e28c9:active {
  background: rgba(46, 128, 254, 0.1);
}
.footer .footer-order.data-v-027e28c9 {
  flex: 1;
  background: #2e80fe;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
  line-height: 88rpx;
}
.footer .footer-order.submitting.data-v-027e28c9 {
  background: #a5c7ff;
  opacity: 0.7;
  pointer-events: none;
}
.footer .footer-order.data-v-027e28c9:active {
  background: #1a6bd8;
}
.footer-single-button .footer-order.data-v-027e28c9 {
  margin: 0;
  flex: 1;
}
/* iOS安全区域适配 */
@supports (bottom: env(safe-area-inset-bottom)) {
.footer.data-v-027e28c9 {
    padding-bottom: calc(38rpx + env(safe-area-inset-bottom));
}
}
/* 购物车弹窗样式 */
.cart-modal.data-v-027e28c9 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.cart-modal-content.data-v-027e28c9 {
  width: 100%;
  max-height: 90vh;
  /* 增加高度 */
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  /*减小圆角 */
  display: flex;
  flex-direction: column;
  -webkit-animation: slideUp-data-v-027e28c9 0.3s ease-out;
          animation: slideUp-data-v-027e28c9 0.3s ease-out;
}
@-webkit-keyframes slideUp-data-v-027e28c9 {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
@keyframes slideUp-data-v-027e28c9 {
from {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
to {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
}
.modal-header.data-v-027e28c9 {
  padding: 24rpx 32rpx;
  /* 调整内边距 */
  display: flex;
  align-items: flex-start;
  /* 调整对齐方式 */
  justify-content: space-between;
  border-bottom: 1rpx solid #f5f5f5;
  /* 更细的边框 */
  position: relative;
  /* 添加相对定位 */
}
.modal-service-info.data-v-027e28c9 {
  display: flex;
  align-items: flex-start;
  /* 调整对齐方式 */
  width: 100%;
  padding: 16rpx 0;
  flex-wrap: wrap;
  /* 允许换行 */
}
.modal-service-image.data-v-027e28c9 {
  width: 120rpx;
  /* 增大图片尺寸 */
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
  /* 防止图片缩小 */
}
.modal-service-details.data-v-027e28c9 {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.modal-service-title.data-v-027e28c9 {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 12rpx;
}
.modal-service-price.data-v-027e28c9 {
  font-size: 36rpx;
  /* 增大价格字体 */
  font-weight: 600;
  color: #FF4D4F;
  /* 更鲜艳的红色 */
  margin-bottom: 24rpx;
  /* 增加间距 */
}
.modal-close.data-v-027e28c9 {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.modal-scroll-content.data-v-027e28c9 {
  flex: 1;
  overflow-y: auto;
  padding: 0 32rpx;
}
.modal-selected-info.data-v-027e28c9 {
  padding: 32rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
  display: flex;
  align-items: flex-start;
}
.modal-selected-title.data-v-027e28c9 {
  font-size: 28rpx;
  color: #999;
  margin-right: 20rpx;
  margin-top: 10rpx;
}
.modal-selected-tags.data-v-027e28c9 {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
}
.modal-tag.data-v-027e28c9 {
  padding: 8rpx 16rpx;
  background: #DCEAFF;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #2E80FE;
  margin: 8rpx 16rpx 8rpx 0;
}
.modal-chol.data-v-027e28c9 {
  border-bottom: 2rpx solid #f5f5f5;
}
.modal-choose.data-v-027e28c9 {
  padding: 32rpx 0;
}
.modal-choose-title.data-v-027e28c9 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}
.modal-choose-title ._span.data-v-027e28c9 {
  color: #E72427;
}
.modal-choose-desc.data-v-027e28c9 {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 24rpx;
}
.modal-cho-box.data-v-027e28c9 {
  display: flex;
  flex-wrap: wrap;
}
.modal-box-item.data-v-027e28c9 {
  padding: 16rpx 24rpx;
  background: #f8f8f8;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  margin: 0 16rpx 16rpx 0;
  position: relative;
  transition: all 0.2s ease;
}
.modal-ok.data-v-027e28c9 {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 24rpx;
  height: 24rpx;
  background: #2E80FE;
  border-radius: 0 8rpx 0 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-input-container.data-v-027e28c9 {
  margin-top: 16rpx;
}
.modal-form-input.data-v-027e28c9 {
  width: 100%;
  height: 88rpx;
  background: #f8f8f8;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.modal-form-input.data-v-027e28c9:focus {
  border-color: #2E80FE;
  background: #fff;
}
.modal-footer.data-v-027e28c9 {
  padding: 32rpx;
  background: #fff;
  border-top: 2rpx solid #f5f5f5;
  flex-shrink: 0;
}
.modal-add-cart-btn.data-v-027e28c9 {
  width: 100%;
  height: 88rpx;
  background: #E72427;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.modal-add-cart-btn.submitting.data-v-027e28c9 {
  background: #f5a5a7;
  opacity: 0.7;
  pointer-events: none;
}
.modal-add-cart-btn.data-v-027e28c9:active {
  background: #c41e20;
}
/* 新增：下单弹窗样式 */
.modal-selected-section.data-v-027e28c9 {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.modal-quantity-section.data-v-027e28c9 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  /* margin-top: 16rpx; remove this margin-top */
  width: 100%;
  /* Ensure it takes full width within service-details */
}
.modal-quantity-title.data-v-027e28c9 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.modal-quantity-control.data-v-027e28c9 {
  display: flex;
  align-items: center;
}
.quantity-btn.data-v-027e28c9 {
  width: 56rpx;
  height: 56rpx;
  border: 1rpx solid #e5e5e5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #333;
  background: #fff;
}
.quantity-btn.data-v-027e28c9:active {
  background: #f5f5f5;
}
.quantity-input.data-v-027e28c9 {
  width: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  margin: 0 20rpx;
}
.modal-address-section.data-v-027e28c9,
.modal-time-section.data-v-027e28c9 {
  padding: 24rpx;
  padding-right: 80rpx;
  border-bottom: 1rpx solid #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.modal-section-title.data-v-027e28c9 {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.section-icon.data-v-027e28c9 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}
.modal-address-content.data-v-027e28c9,
.modal-time-content.data-v-027e28c9 {
  flex: 1;
  margin: 0 16rpx;
  text-align: right;
}
.address-text.data-v-027e28c9 {
  font-size: 28rpx;
  color: #2E80FE;
  margin-bottom: 4rpx;
}
.address-detail.data-v-027e28c9 {
  font-size: 24rpx;
  color: #999;
}
.modal-time-content text.data-v-027e28c9 {
  font-size: 28rpx;
  color: #2E80FE;
}
.modal-urgent-section.data-v-027e28c9 {
  padding: 16rpx 0;
  /* Adjusted padding */
  border-bottom: none;
  /* Removed border, it's now inside modal-service-info */
  width: 100%;
  /* Ensure it takes full width within service-details */
}
.modal-urgent-checkbox.data-v-027e28c9 {
  display: flex;
  align-items: center;
}
.checkbox-icon.data-v-027e28c9 {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  background: #fff;
}
.checkbox-icon.checked.data-v-027e28c9 {
  background: #2E80FE;
  border-color: #2E80FE;
}
.checkbox-label.data-v-027e28c9 {
  font-size: 28rpx;
  color: #333;
}
.modal-notes-section.data-v-027e28c9 {
  padding: 32rpx 0;
  margin-right: 60rpx;
}
.modal-notes-title.data-v-027e28c9 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 24rpx;
}
.modal-notes-textarea.data-v-027e28c9 {
  width: 100%;
  min-height: 160rpx;
  background: #f8f8f8;
  border: 1rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.modal-notes-textarea.data-v-027e28c9:focus {
  border-color: #2E80FE;
  background: #fff;
}
.modal-total-price.data-v-027e28c9 {
  font-size: 32rpx;
  font-weight: 600;
  color: #E72427;
  margin-bottom: 24rpx;
}
.modal-footer-buttons.data-v-027e28c9 {
  display: flex;
  gap: 24rpx;
}
.modal-footer-buttons .modal-add-cart-btn.data-v-027e28c9 {
  flex: 1;
  background: transparent;
  border: 2rpx solid #2E80FE;
  color: #2E80FE;
}
.modal-footer-buttons .modal-add-cart-btn.data-v-027e28c9:active {
  background: rgba(46, 128, 254, 0.1);
}
.modal-order-btn.data-v-027e28c9 {
  width: 100%;
  height: 88rpx;
  background: #FF4D4F;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.modal-order-btn.submitting.data-v-027e28c9 {
  background: #f5a5a7;
  opacity: 0.7;
  pointer-events: none;
}
.modal-order-btn.data-v-027e28c9:active {
  background: #c41e20;
}
/* 时间选择弹窗样式 */
.time-modal.data-v-027e28c9 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.time-modal-content.data-v-027e28c9 {
  width: 100%;
  max-height: 80vh;
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  display: flex;
  flex-direction: column;
  -webkit-animation: slideUp-data-v-027e28c9 0.3s ease-out;
          animation: slideUp-data-v-027e28c9 0.3s ease-out;
}
.time-modal-header.data-v-027e28c9 {
  padding: 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 2rpx solid #f5f5f5;
  flex-shrink: 0;
}
.time-modal-title.data-v-027e28c9 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}
.time-modal-close.data-v-027e28c9 {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.time-date-section.data-v-027e28c9 {
  padding: 32rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-bottom: 2rpx solid #f5f5f5;
}
.time-date-item.data-v-027e28c9 {
  text-align: center;
  font-size: 28rpx;
  color: #333;
  padding: 16rpx 24rpx;
  border-radius: 12rpx;
  transition: all 0.2s ease;
}
.time-date-item.active.data-v-027e28c9 {
  color: #2E80FE;
  background: rgba(46, 128, 254, 0.1);
}
.time-date-item.active .date-str.data-v-027e28c9 {
  color: #2E80FE;
}
.date-str.data-v-027e28c9 {
  font-weight: 500;
  margin-bottom: 8rpx;
}
.date-num.data-v-027e28c9 {
  font-size: 24rpx;
  color: #666;
}
.time-date-item.active .date-num.data-v-027e28c9 {
  color: #2E80FE;
}
.time-slots-section.data-v-027e28c9 {
  flex: 1;
  padding: 32rpx;
  max-height: 400rpx;
}
.time-slots-grid.data-v-027e28c9 {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}
.time-slot-column.data-v-027e28c9 {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.time-slot-item.data-v-027e28c9 {
  height: 80rpx;
  background: #f8f8f8;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 24rpx;
  color: #333;
  text-align: center;
  line-height: 76rpx;
  transition: all 0.2s ease;
}
.time-slot-item.active.data-v-027e28c9 {
  background: #2E80FE;
  border-color: #2E80FE;
  color: #fff;
}
.time-slot-item.disabled.data-v-027e28c9 {
  background: #f0f0f0;
  border-color: #e0e0e0;
  color: #ccc;
  pointer-events: none;
}
.time-modal-footer.data-v-027e28c9 {
  padding: 32rpx;
  border-top: 2rpx solid #f5f5f5;
  flex-shrink: 0;
}
.time-confirm-btn.data-v-027e28c9 {
  width: 100%;
  height: 88rpx;
  background: #2E80FE;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}
.time-confirm-btn.data-v-027e28c9:active {
  background: #1a6bd8;
}
/* 添加新增样式 */
.modal-header-tip.data-v-027e28c9 {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  width: 100%;
  padding: 16rpx 48rpx;
}
.modal-selected-title.data-v-027e28c9 {
  font-size: 28rpx;
  color: #666;
}
.modal-specs-section.data-v-027e28c9 {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
}
.modal-specs-tags.data-v-027e28c9 {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16rpx;
}
.modal-spec-tag.data-v-027e28c9 {
  padding: 8rpx 20rpx;
  background: #f0f9ff;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #2E80FE;
  margin: 8rpx 16rpx 8rpx 0;
}
.modal-notes-title.data-v-027e28c9 {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.notes-limit.data-v-027e28c9 {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
}
.textarea-counter.data-v-027e28c9 {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}
.upload-photo-btn.data-v-027e28c9 {
  width: 160rpx;
  height: 160rpx;
  background: #f8f8f8;
  border: 1rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 24rpx;
}
.upload-photo-btn text.data-v-027e28c9 {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
}
.modal-notes-section.data-v-027e28c9 {
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

