<view class="page data-v-0cdcf780"><view class="main data-v-0cdcf780"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="main_item_already data-v-0cdcf780"><view class="title data-v-0cdcf780">{{item.$orig.payType==-2?'等待客户选择':''}}</view><view class="title data-v-0cdcf780">{{item.$orig.payType==-1?'客户已取消订单':''}}</view><view class="title data-v-0cdcf780">{{item.$orig.payType==1?'客户已选择报价':''}}</view><view class="ok data-v-0cdcf780">您已报价</view><view class="no data-v-0cdcf780">{{"单号："+item.$orig.orderCode}}</view><view class="mid data-v-0cdcf780"><view class="lef data-v-0cdcf780"><image src="{{item.$orig.goodsCover}}" mode class="data-v-0cdcf780"></image><text class="data-v-0cdcf780">{{item.$orig.goodsName}}</text></view></view><view class="bot data-v-0cdcf780"><text class="data-v-0cdcf780">{{item.g0}}</text></view><view class="shifu data-v-0cdcf780"><scroll-view scroll-x="true" class="data-v-0cdcf780"><view class="shifu_item data-v-0cdcf780"><view class="top data-v-0cdcf780"><image src="{{userInfo.avatarUrl?userInfo.avatarUrl:'/static/mine/default_user.png'}}" mode class="data-v-0cdcf780"></image><view class="info data-v-0cdcf780"><view class="name data-v-0cdcf780">{{userInfo.nickName}}</view></view></view><text class="data-v-0cdcf780">{{"￥"+item.$orig.price}}</text></view></scroll-view></view><block wx:if="{{item.$orig.payType==-2}}"><view class="btnbox data-v-0cdcf780"><view data-event-opts="{{[['tap',[['cancelBao',['$0'],[[['list','',index]]]]]]]}}" class="btn can data-v-0cdcf780" bindtap="__e">取消报价</view><view data-event-opts="{{[['tap',[['againBao',['$0'],[[['list','',index]]]]]]]}}" class="btn re data-v-0cdcf780" bindtap="__e">重新报价</view></view></block></view></block><block wx:if="{{loadingStatus}}"><view class="loading-status data-v-0cdcf780"><text class="data-v-0cdcf780">{{loadingText}}</text></view></block><block wx:if="{{$root.g1}}"><view class="empty-data data-v-0cdcf780"><text class="data-v-0cdcf780">暂无数据</text></view></block></view><u-popup vue-id="e27b0cf4-1" show="{{show}}" round="{{10}}" closeable="{{true}}" adjust-position="{{true}}" mode="bottom" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-0cdcf780" bind:__l="__l" vue-slots="{{['default']}}"><scroll-view class="popup-scroll data-v-0cdcf780" scroll-y="true" scroll-into-view="{{scrollToId}}"><view class="box data-v-0cdcf780" id="input-box"><view class="title data-v-0cdcf780">重新报价</view><view class="title2 data-v-0cdcf780">报价金额</view><view class="money data-v-0cdcf780"><u--input bind:input="__e" vue-id="{{('e27b0cf4-2')+','+('e27b0cf4-1')}}" id="price-input" placeholder="请输入报价金额" prefixIcon="rmb" prefixIconStyle="font-size: 22px;color: #909399" type="digit" focus="{{true}}" value="{{input}}" data-event-opts="{{[['^input',[['__set_model',['','input','$event',[]]]]]]}}" class="data-v-0cdcf780" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['confirmBao',['$event']]]]]}}" class="btn data-v-0cdcf780" bindtap="__e">确认报价</view></view></scroll-view></u-popup><u-modal vue-id="e27b0cf4-3" show="{{showCancel}}" title="取消报价" content="确定要取消对本单的报价吗？" showCancelButton="{{true}}" data-event-opts="{{[['^cancel',[['cancelModal']]],['^confirm',[['confirm']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-0cdcf780" bind:__l="__l"></u-modal></view>