{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-index-list/u-index-list.vue?efbb", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-index-list/u-index-list.vue?62ba", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-index-list/u-index-list.vue?ba23", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-index-list/u-index-list.vue?6d13", "uni-app:///node_modules/uview-ui/components/u-index-list/u-index-list.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-index-list/u-index-list.vue?be57", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/node_modules/uview-ui/components/u-index-list/u-index-list.vue?ee06"], "names": ["indexList", "name", "mixins", "options", "virtualHost", "data", "activeIndex", "touchmoveIndex", "letterInfo", "height", "itemHeight", "top", "indicatorHeight", "touching", "scrollTop", "scrollViewHeight", "sys", "scrolling", "scrollIntoView", "computed", "uIndexList", "indicatorTop", "watch", "immediate", "handler", "uni", "created", "mounted", "methods", "init", "touchStart", "pageY", "touchMove", "touchEnd", "getIndexListLetterRect", "resolve", "setIndexListLetterInfo", "size", "customNavHeight", "getIndexListLetter", "setValueForTouch", "getHeaderRect", "dom", "<PERSON><PERSON><PERSON><PERSON>", "len", "children", "anchors", "i", "item", "nextItem"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0TAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvDA;AAAA;AAAA;AAAA;AAA21B,CAAgB,22BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsG/2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARA;EACA;EACA;EACA;IACAA;EACA;EACA;AACA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,eAUA;EACAC;EACAC;EAEA;EACAC;IACAC;EACA;EAEAC;IACA;MACA;MACAC;MACAC;MACA;MACAC;QACAC;QACAC;QACAC;MACA;MACA;MACAC;MACA;MACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA,uBAGA;QAFAV;QACAD;MAEA;IACA;EACA;EACAY;IACA;IACAF;MACAG;MACAC;QAAA;QACAC;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA,IACAC,QACAD,WADAC;MAEA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;;MAEA;MACA;QACA;MACA;MACA,IACAD,QACAC,UADAD;MAEA;MACA;IACA;IACA;IACAE;MAAA;MACA;MACAR;QACA;MACA;IACA;IACA;IACAS;MAAA;MACA;QACA;;QAEA;UACAC;QACA;MASA;IACA;IACA;IACAC;MAAA;MACA;QACA,IACA3B,SACA4B,KADA5B;QAEA;QACA;QACA;QACA;QACA;UAKA;UACA6B;QAEA;UACAA;QACA;QACA;UACA7B;UACA;UACAE;UACAD;QACA;MACA;IACA;IACA;IACA6B;MACA,wBAIA;QAHA5B;QACAF;QACAC;MAEA;;MAIA;MACA;QACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACA8B;MACA;MACA;MACA;;MAMA;MACA;IAUA;IACAC;MAAA;MACA;MACA;QACAC;UACAP;QACA;MACA;IACA;IACA;IACAQ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;gBACAlB;kBACA;gBACA;gBACAX;gBACA8B;gBACAC;gBACAC,0BAoBA;gBACAhC;gBAEAiC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAC,oBACAC,4BACA;gBAAA,MACAnC,oFACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,IAEAmC;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA;cAAA;gBAAA,MAEAnC;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAbAiC;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAiBA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7XA;AAAA;AAAA;AAAA;AAAkmD,CAAgB,sjDAAG,EAAC,C;;;;;;;;;;;ACAtnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-index-list/u-index-list.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-index-list.vue?vue&type=template&id=aecf76f2&scoped=true&\"\nvar renderjs\nimport script from \"./u-index-list.vue?vue&type=script&lang=js&\"\nexport * from \"./u-index-list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-index-list.vue?vue&type=style&index=0&id=aecf76f2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"aecf76f2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-index-list/u-index-list.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-index-list.vue?vue&type=template&id=aecf76f2&scoped=true&\"", "var components\ntry {\n  components = {\n    uTransition: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-transition/u-transition\" */ \"uview-ui/components/u-transition/u-transition.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$u.addUnit(_vm.scrollViewHeight)\n  var g1 = _vm.$u.addUnit(_vm.letterInfo.top || 100)\n  var a0 = {\n    position: \"fixed\",\n    right: \"50px\",\n    top: _vm.$u.addUnit(_vm.indicatorTop),\n    zIndex: 2,\n  }\n  var g2 = _vm.$u.addUnit(_vm.indicatorHeight)\n  var g3 = _vm.$u.addUnit(_vm.indicatorHeight)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        a0: a0,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-index-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-index-list.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-index-list\">\n\t\t<!-- #ifdef APP-NVUE -->\n\t\t<list\n\t\t\t:scrollTop=\"scrollTop\"\n\t\t\tenable-back-to-top\n\t\t\t:offset-accuracy=\"1\"\n\t\t\t:style=\"{\n\t\t\t\tmaxHeight: $u.addUnit(scrollViewHeight)\n\t\t\t}\"\n\t\t\t@scroll=\"scrollHandler\"\n\t\t\tref=\"uList\"\n\t\t>\n\t\t\t<cell\n\t\t\t\tv-if=\"$slots.header\"\n\t\t\t\tref=\"header\"\n\t\t\t>\n\t\t\t\t<slot name=\"header\" />\n\t\t\t</cell>\n\t\t\t<slot />\n\t\t\t<cell v-if=\"$slots.footer\">\n\t\t\t\t<slot name=\"footer\" />\n\t\t\t</cell>\n\t\t</list>\n\t\t<!-- #endif -->\n\t\t<!-- #ifndef APP-NVUE -->\n\t\t<scroll-view\n\t\t\t:scrollTop=\"scrollTop\"\n\t\t\t:scrollIntoView=\"scrollIntoView\"\n\t\t\t:offset-accuracy=\"1\"\n\t\t\t:style=\"{\n\t\t\t\tmaxHeight: $u.addUnit(scrollViewHeight)\n\t\t\t}\"\n\t\t\tscroll-y\n\t\t\t@scroll=\"scrollHandler\"\n\t\t\tref=\"uList\"\n\t\t>\n\t\t\t<view v-if=\"$slots.header\">\n\t\t\t\t<slot name=\"header\" />\n\t\t\t</view>\n\t\t\t<slot />\n\t\t\t<view v-if=\"$slots.footer\">\n\t\t\t\t<slot name=\"footer\" />\n\t\t\t</view>\n\t\t</scroll-view>\n\t\t<!-- #endif -->\n\t\t<view\n\t\t\tclass=\"u-index-list__letter\"\n\t\t\tref=\"u-index-list__letter\"\n\t\t\t:style=\"{ top: $u.addUnit(letterInfo.top || 100) }\"\n\t\t\t@touchstart=\"touchStart\"\n\t\t\************************=\"touchMove\"\n\t\t\***********************=\"touchEnd\"\n\t\t\**************************=\"touchEnd\"\n\t\t>\n\t\t\t<view\n\t\t\t\tclass=\"u-index-list__letter__item\"\n\t\t\t\tv-for=\"(item, index) in uIndexList\"\n\t\t\t\t:key=\"index\"\n\t\t\t\t:style=\"{\n\t\t\t\t\tbackgroundColor: activeIndex === index ? activeColor : 'transparent'\n\t\t\t\t}\"\n\t\t\t>\n\t\t\t\t<text\n\t\t\t\t\tclass=\"u-index-list__letter__item__index\"\n\t\t\t\t\t:style=\"{color: activeIndex === index ? '#fff' : inactiveColor}\"\n\t\t\t\t>{{ item }}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<u-transition\n\t\t\tmode=\"fade\"\n\t\t\t:show=\"touching\"\n\t\t\t:customStyle=\"{\n\t\t\t\tposition: 'fixed',\n\t\t\t\tright: '50px',\n\t\t\t\ttop: $u.addUnit(indicatorTop),\n\t\t\t\tzIndex: 2\n\t\t\t}\"\n\t\t>\n\t\t\t<view\n\t\t\t\tclass=\"u-index-list__indicator\"\n\t\t\t\t:class=\"['u-index-list__indicator--show']\"\n\t\t\t\t:style=\"{\n\t\t\t\t\theight: $u.addUnit(indicatorHeight),\n\t\t\t\t\twidth: $u.addUnit(indicatorHeight)\n\t\t\t\t}\"\n\t\t\t>\n\t\t\t\t<text class=\"u-index-list__indicator__text\">{{ uIndexList[activeIndex] }}</text>\n\t\t\t</view>\n\t\t</u-transition>\n\t</view>\n</template>\n\n<script>\n\tconst indexList = () => {\n\t\tconst indexList = [];\n\t\tconst charCodeOfA = 'A'.charCodeAt(0);\n\t\tfor (let i = 0; i < 26; i++) {\n\t\t\tindexList.push(String.fromCharCode(charCodeOfA + i));\n\t\t}\n\t\treturn indexList;\n\t}\n\timport props from './props.js';\n\t// #ifdef APP-NVUE\n\t// 由于weex为阿里的KPI业绩考核的产物，所以不支持百分比单位，这里需要通过dom查询组件的宽度\n\tconst dom = uni.requireNativePlugin('dom')\n\t// #endif\n\t/**\n\t * IndexList 索引列表\n\t * @description  通过折叠面板收纳内容区域\n\t * @tutorial https://uviewui.com/components/indexList.html\n\t * @property {String}\t\t\tinactiveColor\t右边锚点非激活的颜色 ( 默认 '#606266' )\n\t * @property {String}\t\t\tactiveColor\t\t右边锚点激活的颜色 ( 默认 '#5677fc' )\n\t * @property {Array}\t\t\tindexList\t\t索引字符列表，数组形式\n\t * @property {Boolean}\t\t\tsticky\t\t\t是否开启锚点自动吸顶 ( 默认 true )\n\t * @property {String | Number}\tcustomNavHeight\t自定义导航栏的高度 ( 默认 0 )\n\t * */ \n\texport default {\n\t\tname: 'u-index-list',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\n\t\t// #ifdef MP-WEIXIN\n\t\t// 将自定义节点设置成虚拟的，更加接近Vue组件的表现，能更好的使用flex属性\n\t\toptions: {\n\t\t\tvirtualHost: true\n\t\t},\n\t\t// #endif\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t// 当前正在被选中的字母索引\n\t\t\t\tactiveIndex: -1,\n\t\t\t\ttouchmoveIndex: 1,\n\t\t\t\t// 索引字母的信息\n\t\t\t\tletterInfo: {\n\t\t\t\t\theight: 0,\n\t\t\t\t\titemHeight: 0,\n\t\t\t\t\ttop: 0\n\t\t\t\t},\n\t\t\t\t// 设置字母指示器的高度，后面为了让指示器跟随字母，并将尖角部分指向字母的中部，需要依赖此值\n\t\t\t\tindicatorHeight: 50,\n\t\t\t\t// 字母放大指示器的top值，为了让其指向当前激活的字母\n\t\t\t\t// indicatorTop: 0\n\t\t\t\t// 当前是否正在被触摸状态\n\t\t\t\ttouching: false,\n\t\t\t\t// 滚动条顶部top值\n\t\t\t\tscrollTop: 0,\n\t\t\t\t// scroll-view的高度\n\t\t\t\tscrollViewHeight: 0,\n\t\t\t\t// 系统信息\n\t\t\t\tsys: uni.$u.sys(),\n\t\t\t\tscrolling: false,\n\t\t\t\tscrollIntoView: '',\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 如果有传入外部的indexList锚点数组则使用，否则使用内部生成A-Z字母\n\t\t\tuIndexList() {\n\t\t\t\treturn this.indexList.length ? this.indexList : indexList()\n\t\t\t},\n\t\t\t// 字母放大指示器的top值，为了让其指向当前激活的字母\n\t\t\tindicatorTop() {\n\t\t\t\tconst {\n\t\t\t\t\ttop,\n\t\t\t\t\titemHeight\n\t\t\t\t} = this.letterInfo\n\t\t\t\treturn Math.floor(top + itemHeight * this.activeIndex + itemHeight / 2 - this.indicatorHeight / 2)\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\t// 监听字母索引的变化，重新设置尺寸\n\t\t\tuIndexList: {\n\t\t\t\timmediate: true,\n\t\t\t\thandler() {\n\t\t\t\t\tuni.$u.sleep().then(() => {\n\t\t\t\t\t\tthis.setIndexListLetterInfo()\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.children = []\n\t\t\tthis.anchors = []\n\t\t\tthis.init()\n\t\t},\n\t\tmounted() {\n\t\t\tthis.setIndexListLetterInfo()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\t// 设置列表的高度为整个屏幕的高度\n\t\t\t\t//减去this.customNavHeight，并将this.scrollViewHeight设置为maxHeight\n\t\t\t\t//解决当u-index-list组件放在tabbar页面时,scroll-view内容较少时，还能滚动\n\t\t\t\tthis.scrollViewHeight = this.sys.windowHeight - this.customNavHeight\n\t\t\t},\n\t\t\t// 索引列表被触摸\n\t\t\ttouchStart(e) {\n\t\t\t\t// 获取触摸点信息\n\t\t\t\tconst touchStart = e.changedTouches[0]\n\t\t\t\tif (!touchStart) return\n\t\t\t\tthis.touching = true\n\t\t\t\tconst {\n\t\t\t\t\tpageY\n\t\t\t\t} = touchStart\n\t\t\t\t// 根据当前触摸点的坐标，获取当前触摸的为第几个字母\n\t\t\t\tconst currentIndex = this.getIndexListLetter(pageY)\n\t\t\t\tthis.setValueForTouch(currentIndex)\n\t\t\t},\n\t\t\t// 索引字母列表被触摸滑动中\n\t\t\ttouchMove(e) {\n\t\t\t\t// 获取触摸点信息\n\t\t\t\tlet touchMove = e.changedTouches[0]\n\t\t\t\tif (!touchMove) return;\n\n\t\t\t\t// 滑动结束后迅速开始第二次滑动时候 touching 为 false 造成不显示 indicator 问题\n\t\t\t\tif (!this.touching) {\n\t\t\t\t\tthis.touching = true\n\t\t\t\t}\n\t\t\t\tconst {\n\t\t\t\t\tpageY\n\t\t\t\t} = touchMove\n\t\t\t\tconst currentIndex = this.getIndexListLetter(pageY)\n\t\t\t\tthis.setValueForTouch(currentIndex)\n\t\t\t},\n\t\t\t// 触摸结束\n\t\t\ttouchEnd(e) {\n\t\t\t\t// 延时一定时间后再隐藏指示器，为了让用户看的更直观，同时也是为了消除快速切换u-transition的show带来的影响\n\t\t\t\tuni.$u.sleep(300).then(() => {\n\t\t\t\t\tthis.touching = false\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取索引列表的尺寸以及单个字符的尺寸信息\n\t\t\tgetIndexListLetterRect() {\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\t// 延时一定时间，以获取dom尺寸\n\t\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\t\tthis.$uGetRect('.u-index-list__letter').then(size => {\n\t\t\t\t\t\tresolve(size)\n\t\t\t\t\t})\n\t\t\t\t\t// #endif\n\n\t\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t\tconst ref = this.$refs['u-index-list__letter']\n\t\t\t\t\tdom.getComponentRect(ref, res => {\n\t\t\t\t\t\tresolve(res.size)\n\t\t\t\t\t})\n\t\t\t\t\t// #endif\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 设置indexList索引的尺寸信息\n\t\t\tsetIndexListLetterInfo() {\n\t\t\t\tthis.getIndexListLetterRect().then(size => {\n\t\t\t\t\tconst {\n\t\t\t\t\t\theight\n\t\t\t\t\t} = size\n\t\t\t\t\tconst sys = uni.$u.sys()\n\t\t\t\t\tconst windowHeight = sys.windowHeight\n\t\t\t\t\tlet customNavHeight = 0\n\t\t\t\t\t// 消除各端导航栏非原生和原生导致的差异，让索引列表字母对屏幕垂直居中\n\t\t\t\t\tif (this.customNavHeight == 0) {\n\t\t\t\t\t\t// #ifdef H5\n\t\t\t\t\t\tcustomNavHeight = sys.windowTop\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t// #ifndef H5\n\t\t\t\t\t\t// 在非H5中，为原生导航栏，其高度不算在windowHeight内，这里设置为负值，后面相加时变成减去其高度的一半\n\t\t\t\t\t\tcustomNavHeight = -(sys.statusBarHeight + 44)\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t} else {\n\t\t\t\t\t\tcustomNavHeight = uni.$u.getPx(this.customNavHeight)\n\t\t\t\t\t}\n\t\t\t\t\tthis.letterInfo = {\n\t\t\t\t\t\theight,\n\t\t\t\t\t\t// 为了让字母列表对屏幕绝对居中，让其对导航栏进行修正，也即往上偏移导航栏的一半高度\n\t\t\t\t\t\ttop: (windowHeight - height) / 2 + customNavHeight / 2,\n\t\t\t\t\t\titemHeight: Math.floor(height / this.uIndexList.length)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 获取当前被触摸的索引字母\n\t\t\tgetIndexListLetter(pageY) {\n\t\t\t\tconst {\n\t\t\t\t\ttop,\n\t\t\t\t\theight,\n\t\t\t\t\titemHeight\n\t\t\t\t} = this.letterInfo\n\t\t\t\t// 对H5的pageY进行修正，这是由于uni-app自作多情在H5中将触摸点的坐标跟H5的导航栏结合导致的问题\n\t\t\t\t// #ifdef H5\n\t\t\t\tpageY += uni.$u.sys().windowTop\n\t\t\t\t// #endif\n\t\t\t\t// 对第一和最后一个字母做边界处理，因为用户可能在字母列表上触摸到两端的尽头后依然继续滑动\n\t\t\t\tif (pageY < top) {\n\t\t\t\t\treturn 0\n\t\t\t\t} else if (pageY >= top + height) {\n\t\t\t\t\t// 如果超出了，取最后一个字母\n\t\t\t\t\treturn this.uIndexList.length - 1\n\t\t\t\t} else {\n\t\t\t\t\t// 将触摸点的Y轴偏移值，减去索引字母的top值，除以每个字母的高度，即可得到当前触摸点落在哪个字母上\n\t\t\t\t\treturn Math.floor((pageY - top) / itemHeight);\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 设置各项由触摸而导致变化的值\n\t\t\tsetValueForTouch(currentIndex) {\n\t\t\t\t// 如果偏移量太小，前后得出的会是同一个索引字母，为了防抖，进行返回\n\t\t\t\tif (currentIndex === this.activeIndex) return\n\t\t\t\tthis.activeIndex = currentIndex\n\t\t\t\t// #ifndef APP-NVUE || MP-WEIXIN\n\t\t\t\t// 在非nvue中，由于anchor和item都在u-index-item中，所以需要对index-item进行偏移\n\t\t\t\tthis.scrollIntoView = `u-index-item-${this.uIndexList[currentIndex].charCodeAt(0)}`\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef MP-WEIXIN\n\t\t\t\t// 微信小程序下，scroll-view的scroll-into-view属性无法对slot中的内容的id生效，只能通过设置scrollTop的形式去移动滚动条\n\t\t\t\tthis.scrollTop = this.children[currentIndex].top\n\t\t\t\t// #endif\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// 在nvue中，由于cell和header为同级元素，所以实际是需要对header(anchor)进行偏移\n\t\t\t\tconst anchor = `u-index-anchor-${this.uIndexList[currentIndex]}`\n\t\t\t\tdom.scrollToElement(this.anchors[currentIndex].$refs[anchor], {\n\t\t\t\t\toffset: 0,\n\t\t\t\t\tanimated: false\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tgetHeaderRect() {\n\t\t\t\t// 获取header slot的高度，因为list组件中获取元素的尺寸是没有top值的\n\t\t\t\treturn new Promise(resolve => {\n\t\t\t\t\tdom.getComponentRect(this.$refs.header, res => {\n\t\t\t\t\t\tresolve(res.size)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t\t// scroll-view的滚动事件\n\t\t\tasync scrollHandler(e) {\n\t\t\t\tif (this.touching || this.scrolling) return\n\t\t\t\t// 每过一定时间取样一次，减少资源损耗以及可能带来的卡顿\n\t\t\t\tthis.scrolling = true\n\t\t\t\tuni.$u.sleep(10).then(() => {\n\t\t\t\t\tthis.scrolling = false\n\t\t\t\t})\n\t\t\t\tlet scrollTop = 0\n\t\t\t\tconst len = this.children.length\n\t\t\t\tlet children = this.children\n\t\t\t\tconst anchors = this.anchors\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t// nvue下获取的滚动条偏移为负数，需要转为正数\n\t\t\t\tscrollTop = Math.abs(e.contentOffset.y)\n\t\t\t\t// 获取header slot的尺寸信息\n\t\t\t\tconst header = await this.getHeaderRect()\n\t\t\t\t// item的top值，在nvue下，模拟出的anchor的top，类似非nvue下的index-item的top\n\t\t\t\tlet top = header.height\n\t\t\t\t// 由于list组件无法获取cell的top值，这里通过header slot和各个item之间的height，模拟出类似非nvue下的位置信息\n\t\t\t\tchildren = this.children.map((item, index) => {\n\t\t\t\t\tconst child = {\n\t\t\t\t\t\theight: item.height,\n\t\t\t\t\t\ttop\n\t\t\t\t\t}\n\t\t\t\t\t// 进行累加，给下一个item提供计算依据\n\t\t\t\t\ttop += item.height + anchors[index].height\n\t\t\t\t\treturn child\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\t// 非nvue通过detail获取滚动条位移\n\t\t\t\tscrollTop = e.detail.scrollTop\n\t\t\t\t// #endif\n\t\t\t\tfor (let i = 0; i < len; i++) {\n\t\t\t\t\tconst item = children[i],\n\t\t\t\t\t\tnextItem = children[i + 1]\n\t\t\t\t\t// 如果滚动条高度小于第一个item的top值，此时无需设置任意字母为高亮\n\t\t\t\t\tif (scrollTop <= children[0].top || scrollTop >= children[len - 1].top + children[len -\n\t\t\t\t\t\t\t1].height) {\n\t\t\t\t\t\tthis.activeIndex = -1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t} else if (!nextItem) { \n\t\t\t\t\t\t// 当不存在下一个item时，意味着历遍到了最后一个\n\t\t\t\t\t\tthis.activeIndex = len - 1\n\t\t\t\t\t\tbreak\n\t\t\t\t\t} else if (scrollTop > item.top && scrollTop < nextItem.top) {\n\t\t\t\t\t\tthis.activeIndex = i\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-index-list {\n\n\t\t&__letter {\n\t\t\tposition: fixed;\n\t\t\tright: 0;\n\t\t\ttext-align: center;\n\t\t\tz-index: 3;\n\t\t\tpadding: 0 6px;\n\n\t\t\t&__item {\n\t\t\t\twidth: 16px;\n\t\t\t\theight: 16px;\n\t\t\t\tborder-radius: 100px;\n\t\t\t\tmargin: 1px 0;\n\t\t\t\t@include flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t&--active {\n\t\t\t\t\tbackground-color: $u-primary;\n\t\t\t\t}\n\n\t\t\t\t&__index {\n\t\t\t\t\tfont-size: 12px;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tline-height: 12px;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t&__indicator {\n\t\t\twidth: 50px;\n\t\t\theight: 50px;\n\t\t\tborder-radius: 100px 100px 0 100px;\n\t\t\ttext-align: center;\n\t\t\tcolor: #ffffff;\n\t\t\tbackground-color: #c9c9c9;\n\t\t\ttransform: rotate(-45deg);\n\t\t\t@include flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\n\t\t\t&__text {\n\t\t\t\tfont-size: 28px;\n\t\t\t\tline-height: 28px;\n\t\t\t\tfont-weight: bold;\n\t\t\t\tcolor: #fff;\n\t\t\t\ttransform: rotate(45deg);\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-index-list.vue?vue&type=style&index=0&id=aecf76f2&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-index-list.vue?vue&type=style&index=0&id=aecf76f2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755670632053\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}