@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-f98852ca {
  background-color: #f8f8f8;
  height: 100vh;
  overflow: auto;
  padding: 40rpx 30rpx;
}
.page .header.data-v-f98852ca {
  width: 690rpx;
  height: 274rpx;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  padding: 28rpx 36rpx;
}
.page .header .top.data-v-f98852ca {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 42rpx;
  border-bottom: 2rpx solid #E9E9E9;
}
.page .header .top .left .name.data-v-f98852ca {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}
.page .header .top .left .time.data-v-f98852ca {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .header .top .right image.data-v-f98852ca {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}
.page .header .bott.data-v-f98852ca {
  height: 100rpx;
  display: flex;
  align-items: center;
}
.page .header .bott text.data-v-f98852ca {
  margin-left: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #2E80FE;
}
.page .header .bott .box.data-v-f98852ca {
  width: 42rpx;
  height: 42rpx;
  background: #2E80FE;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .after-sales-btn.data-v-f98852ca {
  width: 686rpx;
  height: 88rpx;
  font-weight: 700;
  background: #FFFFFF;
  border-radius: 20rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #2E80FE;
  line-height: 88rpx;
  text-align: center;
  margin: 16rpx auto;
}
.page .after-sales-input.data-v-f98852ca {
  padding: 20rpx;
}
.page .schedule.data-v-f98852ca {
  width: 690rpx;
  padding: 32rpx 36rpx;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
}
.page .schedule .slot-icon image.data-v-f98852ca {
  width: 32rpx;
  height: 32rpx;
}
.page .schedule.data-v-f98852ca  .u-steps-item__line {
  background-color: #000 !important;
}
.page .info.data-v-f98852ca {
  margin-top: 20rpx;
  width: 690rpx;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  padding: 28rpx 36rpx;
}
.page .info .title.data-v-f98852ca {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 26rpx;
}
.page .info .info_item.data-v-f98852ca {
  display: flex;
  padding: 24rpx 0;
  align-items: center;
  justify-content: space-between;
  border-top: 2rpx solid #E9E9E9;
}
.page .info .info_item .left.data-v-f98852ca {
  font-size: 28rpx;
  font-weight: 400;
  color: #ADADAD;
}
.page .info .info_item .right.data-v-f98852ca {
  max-width: 500rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  text-align: right;
}
.page .info .service-info .goods-list.data-v-f98852ca {
  margin-top: 20rpx;
}
.page .info .service-info .goods-card.data-v-f98852ca {
  margin-bottom: 20rpx;
  padding: 20rpx 0;
  border-top: 2rpx solid #E9E9E9;
}
.page .info .service-info .goods-card.data-v-f98852ca:last-child {
  margin-bottom: 0;
}
.page .info .service-info .goods-card .goods-main.data-v-f98852ca {
  display: flex;
  align-items: flex-start;
}
.page .info .service-info .goods-card .goods-main .goods-image.data-v-f98852ca {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
}
.page .info .service-info .goods-card .goods-main .goods-content.data-v-f98852ca {
  flex: 1;
  min-width: 0;
}
.page .info .service-info .goods-card .goods-main .goods-content .goods-name.data-v-f98852ca {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}
.page .info .service-info .goods-card .goods-main .goods-content .goods-details .detail-item.data-v-f98852ca {
  margin-bottom: 8rpx;
}
.page .info .service-info .goods-card .goods-main .goods-content .goods-details .detail-item .detail-text.data-v-f98852ca {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.3;
}
.page .info .service-info .goods-card .goods-main .goods-right.data-v-f98852ca {
  flex-shrink: 0;
  text-align: right;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  height: 120rpx;
}
.page .info .service-info .goods-card .goods-main .goods-right .goods-price.data-v-f98852ca {
  font-size: 32rpx;
  font-weight: 500;
  color: #FF4444;
  margin-bottom: 8rpx;
}
.page .info .service-info .goods-card .goods-main .goods-right .goods-num.data-v-f98852ca {
  font-size: 28rpx;
  color: #999999;
}
.page .btn.data-v-f98852ca {
  margin: 0 auto;
  margin-top: 40rpx;
  width: 686rpx;
  height: 88rpx;
  background: #2E80FE;
  border-radius: 44rpx 44rpx 44rpx 44rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 88rpx;
  text-align: center;
}

