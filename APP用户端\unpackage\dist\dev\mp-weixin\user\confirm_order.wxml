<view class="page data-v-19041659"><u-modal vue-id="077f35cd-1" show="{{showChoose||showYh}}" content="{{content}}" class="data-v-19041659" bind:__l="__l"></u-modal><view class="choose_yh data-v-19041659" style="{{(showYh?'':'height:0')}}"><view class="head data-v-19041659">优惠券</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="close data-v-19041659" bindtap="__e"><image src="../static/images/9397.png" mode class="data-v-19041659"></image></view><block wx:if="{{$root.g0}}"><u-empty vue-id="077f35cd-2" mode="coupon" icon="http://cdn.uviewui.com/uview/empty/coupon.png" class="data-v-19041659" bind:__l="__l"></u-empty></block><block wx:else><scroll-view style="height:832rpx;" scroll-y="true" class="data-v-19041659"><block wx:for="{{couponlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cou_item data-v-19041659"><view class="top data-v-19041659"><block wx:if="{{item.type==0}}"><view class="box1 data-v-19041659"><label class="_span data-v-19041659">满</label>{{item.full}}<label class="_span data-v-19041659">减</label>{{item.discount+''}}</view></block><block wx:else><view class="box1 data-v-19041659"><label class="_span data-v-19041659">￥</label>{{item.discount}}</view></block><view class="box2 data-v-19041659"><text class="data-v-19041659">{{item.title}}</text><block wx:if="{{item.start_time==0}}"><label class="_span data-v-19041659">{{"有效期：自领券日起"+item.day+"天"}}</label></block><block wx:else><label class="_span data-v-19041659">{{"有效期："+item.start_time}}</label></block></view><view data-event-opts="{{[['tap',[['chooseItemyh',['$0'],[[['couponlist','',index]]]]]]]}}" class="box3 data-v-19041659" style="{{(item.choose?'background:#2E80FE;border:2rpx solid #2E80FE':'')}}" bindtap="__e"><u-icon vue-id="{{'077f35cd-3-'+index}}" name="checkbox-mark" color="#fff" size="16" class="data-v-19041659" bind:__l="__l"></u-icon></view></view><view class="bottom data-v-19041659">{{''+item.rule+''}}</view></view></block><view class="noYh data-v-19041659"><view class="left data-v-19041659">不使用优惠券</view><view data-event-opts="{{[['tap',[['chooseNotyh']]]]}}" class="right data-v-19041659" style="{{(notYh?'background:#2E80FE;border:2rpx solid #2E80FE':'')}}" bindtap="__e"><u-icon vue-id="077f35cd-4" name="checkbox-mark" color="#fff" size="16" class="data-v-19041659" bind:__l="__l"></u-icon></view></view><view class="notcan data-v-19041659">不可使用优惠券</view><block wx:for="{{nocouponlist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="cou_item data-v-19041659" style="border:2rpx solid #ADADAD;background:#fff;"><view class="top data-v-19041659" style="border-bottom:2rpx dashed #ADADAD;"><block wx:if="{{item.type==0}}"><view class="box1 data-v-19041659" style="color:#ADADAD;"><label class="_span data-v-19041659">满</label>{{item.full}}<label class="_span data-v-19041659">减</label>{{item.discount+''}}</view></block><view class="box1 data-v-19041659" style="color:#ADADAD;"><label class="_span data-v-19041659">￥</label>{{item.discount}}</view><view class="box2 data-v-19041659"><text style="color:#ADADAD;" class="data-v-19041659">{{item.title}}</text><block wx:if="{{item.start_time==0}}"><label class="_span data-v-19041659">{{"有效期：自领券日起"+item.day+"天"}}</label></block><block wx:else><label class="_span data-v-19041659">{{"生效时间："+item.start_time}}</label></block></view></view><view class="bottom data-v-19041659">{{''+item.rule+''}}</view></view></block></scroll-view></block></view><view class="choose_time data-v-19041659" style="{{(showChoose?'':'height:0')}}"><view class="head data-v-19041659">请选择时间</view><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" class="close data-v-19041659" bindtap="__e"><image src="../static/images/9397.png" mode class="data-v-19041659"></image></view><view class="date data-v-19041659"><block wx:for="{{dateArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tapDate',['$0',index],[[['dateArr','',index]]]]]]]}}" class="date_item data-v-19041659" style="{{(currentDate==index?'color:#2E80FE;':'')}}" bindtap="__e"><view class="data-v-19041659">{{item.str}}</view><view class="data-v-19041659">{{item.date}}</view><view class="hk data-v-19041659" style="{{(currentDate==index?'':'display:none;')}}"></view></view></block></view><scroll-view class="time_all data-v-19041659" scroll-y="true"><view class="time_columns data-v-19041659"><view class="time_column data-v-19041659"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.time&&item.time1&&item.time2}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" data-event-params="{{({item,index})}}" class="time_item data-v-19041659" style="{{(item.disabled?'background-color:#adadad;color:#fff;':currentTime===index?'background-color:#2E80FE;color:#fff;':'')}}" bindtap="__e">{{''+item.time+''}}</view></block></block></view><view class="time_column data-v-19041659"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index"><block wx:if="{{item.time&&item.time1&&item.time2}}"><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" data-event-params="{{({item,index})}}" class="time_item data-v-19041659" style="{{(item.disabled?'background-color:#adadad;color:#fff;':currentTime===index+6?'background-color:#2E80FE;color:#fff;':'')}}" bindtap="__e">{{''+item.time+''}}</view></block></block></view></view></scroll-view><view data-event-opts="{{[['tap',[['confirmTime',['$event']]]]]}}" class="btn data-v-19041659" bindtap="__e">确定预约时间</view></view><view data-event-opts="{{[['tap',[['goUrl',['$event']]]]]}}" class="address data-v-19041659" bindtap="__e"><view class="left data-v-19041659"><view class="top data-v-19041659"><image src="../static/images/position.png" mode class="data-v-19041659"></image><text style="color:#599eff;" class="data-v-19041659">{{mrAddress.address?mrAddress.address:'请先添加地址哦'}}</text></view><view class="bottom data-v-19041659">{{mrAddress.address?mrAddress.userName+mrAddress.mobile:''}}</view></view><u-icon vue-id="077f35cd-5" name="arrow-right" color="#333333" size="14" class="data-v-19041659" bind:__l="__l"></u-icon></view><view data-event-opts="{{[['tap',[['e4',['$event']]]]]}}" class="time data-v-19041659" bindtap="__e"><view class="left data-v-19041659"><image src="../static/images/clock.png" mode class="data-v-19041659"></image><text class="data-v-19041659">{{conDate+(conTime?' '+conTime:'')}}</text></view><u-icon vue-id="077f35cd-6" name="arrow-right" color="#333333" size="14" class="data-v-19041659" bind:__l="__l"></u-icon></view><view class="fg data-v-19041659"></view><view class="main data-v-19041659"><block wx:for="{{newItemArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="main_item data-v-19041659"><image src="{{item.cover}}" mode class="data-v-19041659"></image><view class="right data-v-19041659"><view class="title data-v-19041659">{{item.title}}</view><view class="price data-v-19041659"><text class="data-v-19041659">{{"￥"+item.price+"/台"}}</text><u-number-box bind:input="__e" vue-id="{{'077f35cd-7-'+index}}" min="{{1}}" value="{{item.num}}" data-event-opts="{{[['^input',[['__set_model',['$0','num','$event',[]],[[['newItemArr','',index]]]]]]]}}" class="data-v-19041659" bind:__l="__l"></u-number-box></view></view></view></block><block wx:if="{{needShow}}"><view data-event-opts="{{[['tap',[['expandAll',['$event']]]]]}}" class="expand data-v-19041659" bindtap="__e">{{''+(showEx?'展开详情':'收起')+''}}<view class="icon_box data-v-19041659"><u-icon vue-id="077f35cd-8" name="{{showEx?'arrow-down':'arrow-up'}}" color="#ADADAD" size="14" class="data-v-19041659" bind:__l="__l"></u-icon></view></view></block></view><view class="data-v-19041659"><u-checkbox-group bind:change="__e" vue-id="077f35cd-9" data-event-opts="{{[['^change',[['checkboxChange']]]]}}" class="data-v-19041659" bind:__l="__l" vue-slots="{{['default']}}"><u-checkbox bind:input="__e" vue-id="{{('077f35cd-10')+','+('077f35cd-9')}}" name="urgent" shape="circle" label="是否加急" value="{{isUrgent}}" data-event-opts="{{[['^input',[['__set_model',['','isUrgent','$event',[]]]]]]}}" class="data-v-19041659" bind:__l="__l"></u-checkbox></u-checkbox-group></view><view class="fg data-v-19041659"></view><view class="notes data-v-19041659"><view class="title data-v-19041659">服务备注</view><textarea cols="25" rows="5" placeholder="想要额外嘱咐工作人员的可以备注哦~" data-event-opts="{{[['input',[['__set_model',['','notes','$event',[]]]]]]}}" value="{{notes}}" bindinput="__e" class="data-v-19041659"></textarea></view><view class="fg data-v-19041659"></view><view class="footer data-v-19041659"><view class="left data-v-19041659">{{"总计￥"+allMoney}}</view><view data-event-opts="{{[['tap',[['addcar',['$event']]]]]}}" class="{{['mid','data-v-19041659',(isSubmitting)?'disabled':'']}}" bindtap="__e">{{''+(isSubmitting?'加入中...':'加入购物车')+''}}</view><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="{{['right','data-v-19041659',(isSubmitting)?'disabled':'']}}" bindtap="__e">{{''+(isSubmitting?'提交中...':'立即下单')+''}}</view></view></view>