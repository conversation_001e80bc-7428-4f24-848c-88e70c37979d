<view class="page data-v-a95b2d86"><u-modal vue-id="8209812e-1" show="{{showChoose}}" content="{{content}}" class="data-v-a95b2d86" bind:__l="__l"></u-modal><view class="choose_time data-v-a95b2d86" style="{{(showChoose?'':'height:0')}}"><view class="head data-v-a95b2d86">请选择时间</view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="close data-v-a95b2d86" bindtap="__e"><image src="../static/images/9397.png" mode class="data-v-a95b2d86"></image></view><view class="date data-v-a95b2d86"><block wx:for="{{dateArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['tapDate',['$0',index],[[['dateArr','',index]]]]]]]}}" class="date_item data-v-a95b2d86" style="{{(currentDate==index?'color:#2E80FE;':'')}}" bindtap="__e"><view class="data-v-a95b2d86">{{item.str}}</view><view class="data-v-a95b2d86">{{item.date}}</view><view class="hk data-v-a95b2d86" style="{{(currentDate==index?'':'display:none;')}}"></view></view></block></view><scroll-view class="time_all data-v-a95b2d86" scroll-y="true"><view class="time_columns data-v-a95b2d86"><view class="time_column data-v-a95b2d86"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.time&&item.time1&&item.time2}}"><view data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({item,index})}}" class="time_item data-v-a95b2d86" style="{{(item.disabled?'background-color:#adadad;color:#fff;':currentTime===index?'background-color:#2E80FE;color:#fff;':'')}}" bindtap="__e">{{''+item.time+''}}</view></block></block></view><view class="time_column data-v-a95b2d86"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{item.time&&item.time1&&item.time2}}"><view data-event-opts="{{[['tap',[['e2',['$event']]]]]}}" data-event-params="{{({item,index})}}" class="time_item data-v-a95b2d86" style="{{(item.disabled?'background-color:#adadad;color:#fff;':currentTime===index+6?'background-color:#2E80FE;color:#fff;':'')}}" bindtap="__e">{{''+item.time+''}}</view></block></block></view></view></scroll-view><view data-event-opts="{{[['tap',[['confirmTime',['$event']]]]]}}" class="btn data-v-a95b2d86" bindtap="__e">确定预约时间</view></view><view data-event-opts="{{[['tap',[['goUrl',['$event']]]]]}}" class="address data-v-a95b2d86" bindtap="__e"><view class="left data-v-a95b2d86"><view class="top data-v-a95b2d86"><image src="../static/images/position.png" mode class="data-v-a95b2d86"></image><text style="color:#599eff;" class="data-v-a95b2d86">{{mrAddress.address?mrAddress.address:'请先添加地址哦'}}</text></view><view class="bottom data-v-a95b2d86">{{mrAddress.address?mrAddress.userName+mrAddress.mobile:''}}</view></view><u-icon vue-id="8209812e-2" name="arrow-right" color="#333333" size="14" class="data-v-a95b2d86" bind:__l="__l"></u-icon></view><view data-event-opts="{{[['tap',[['e3',['$event']]]]]}}" class="time data-v-a95b2d86" bindtap="__e"><view class="left data-v-a95b2d86"><image src="../static/images/clock.png" mode class="data-v-a95b2d86"></image><text class="data-v-a95b2d86">{{conDate+(conTime?' '+conTime:'')}}</text></view><u-icon vue-id="8209812e-3" name="arrow-right" color="#333333" size="14" class="data-v-a95b2d86" bind:__l="__l"></u-icon></view><view class="fg data-v-a95b2d86"></view><view class="main data-v-a95b2d86"><block wx:for="{{$root.l4}}" wx:for-item="group" wx:for-index="groupIndex" wx:key="groupIndex"><block class="data-v-a95b2d86"><block wx:if="{{groupIndex>0}}"><view class="service-divider data-v-a95b2d86"></view></block><block wx:if="{{group.g0>0}}"><view class="group-header data-v-a95b2d86"><view class="group-title data-v-a95b2d86">{{group.m0}}</view><view class="urgent-option data-v-a95b2d86"><checkbox style="transform:scale(0.7);" checked="{{group.$orig.urgent===1}}" data-event-opts="{{[['tap',[['toggleGroupUrgent',['$0'],[[['groupedCartItems','',groupIndex]]]]]]]}}" bindtap="__e" class="data-v-a95b2d86"></checkbox><text class="urgent-text data-v-a95b2d86">加急</text></view></view></block><view class="service-items data-v-a95b2d86"><block wx:for="{{group.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="main_item data-v-a95b2d86"><image src="{{item.m1}}" mode class="data-v-a95b2d86"></image><view class="right data-v-a95b2d86"><view class="title data-v-a95b2d86">{{item.m2}}</view><block wx:if="{{item.g1}}"><view class="selected-options data-v-a95b2d86"><text class="selected-label data-v-a95b2d86">已选：</text><block wx:for="{{item.l2}}" wx:for-item="setting" wx:for-index="idx" wx:key="idx"><text class="selected-value data-v-a95b2d86">{{''+setting.$orig.val}}<block wx:if="{{idx!==setting.g2-2}}"><text class="data-v-a95b2d86">,</text></block></text></block></view></block><view class="price data-v-a95b2d86"><text class="data-v-a95b2d86">{{"￥"+item.m3+"/台"}}</text><u-number-box vue-id="{{'8209812e-4-'+groupIndex+'-'+index}}" min="{{1}}" value="{{item.$orig.num}}" data-event-opts="{{[['^change',[['e4']]],['^input',[['__set_model',['$0','num','$event',[]],[[['groupedCartItems','',groupIndex],['items','',index]]]]]]]}}" data-event-params="{{({item:item.$orig})}}" bind:change="__e" bind:input="__e" class="data-v-a95b2d86" bind:__l="__l"></u-number-box></view></view></view></block></view></block></block></view><view class="fg data-v-a95b2d86"></view><view class="fg data-v-a95b2d86"></view><view class="footer data-v-a95b2d86"><view class="left data-v-a95b2d86">{{"总计￥"+totalAmount}}</view><view data-event-opts="{{[['tap',[['submitOrder',['$event']]]]]}}" class="{{['right','data-v-a95b2d86',(isSubmitting)?'disabled':'']}}" bindtap="__e">{{''+(isSubmitting?'提交中...':'立即下单')+''}}</view></view></view>