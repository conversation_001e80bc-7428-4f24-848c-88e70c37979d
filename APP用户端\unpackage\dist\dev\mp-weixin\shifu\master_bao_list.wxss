@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-0cdcf780 {
  background-color: #F8F8F8;
  min-height: 100vh;
}
.page .popup-scroll.data-v-0cdcf780 {
  max-height: 60vh;
}
.page .box.data-v-0cdcf780 {
  padding: 40rpx 30rpx;
}
.page .box .title.data-v-0cdcf780 {
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .box .title2.data-v-0cdcf780 {
  margin-top: 32rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #171717;
}
.page .box .money.data-v-0cdcf780 {
  margin-bottom: 20rpx;
}
.page .box .btn.data-v-0cdcf780 {
  margin: 0 auto;
  margin-top: 42rpx;
  width: 688rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 12rpx;
  line-height: 98rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
}
.page .main.data-v-0cdcf780 {
  padding: 40rpx odan30rpx;
}
.page .main .main_item_already.data-v-0cdcf780 {
  padding: 28rpx 36rpx;
  background-color: #fff;
  border-radius: 24rpx;
  margin-bottom: 20rpx;
}
.page .main .main_item_already .title.data-v-0cdcf780 {
  font-size: 40rpx;
  font-weight: 600;
  color: #333333;
}
.page .main .main_item_already .ok.data-v-0cdcf780 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #E72427;
}
.page .main .main_item_already .no.data-v-0cdcf780 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item_already .mid.data-v-0cdcf780 {
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .main .main_item_already .mid .lef.data-v-0cdcf780 {
  display: flex;
  align-items: center;
}
.page .main .main_item_already .mid .lef image.data-v-0cdcf780 {
  width: 120rpx;
  height: 120rpx;
}
.page .main .main_item_already .mid .lef text.data-v-0cdcf780 {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  margin-left: 30rpx;
  max-width: 350rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item_already .bot.data-v-0cdcf780 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .main .main_item_already .shifu.data-v-0cdcf780 {
  margin-top: 20rpx;
}
.page .main .main_item_already .shifu scroll-view.data-v-0cdcf780 {
  width: 100%;
  white-space: nowrap;
}
.page .main .main_item_already .shifu scroll-view .shifu_item.data-v-0cdcf780 {
  display: inline-block;
  margin-right: 28rpx;
}
.page .main .main_item_already .shifu scroll-view .shifu_item .top.data-v-0cdcf780 {
  display: flex;
}
.page .main .main_item_already .shifu scroll-view .shifu_item .top image.data-v-0cdcf780 {
  width: 92rpx;
  height: 92rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}
.page .main .main_item_already .shifu scroll-view .shifu_item .top .info .name.data-v-0cdcf780 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.page .main .main_item_already .shifu scroll-view .shifu_item text.data-v-0cdcf780 {
  font-size: 22rpx;
  font-weight: 500;
  color: #E72427;
  text-align: center;
}
.page .main .main_item_already .btnbox.data-v-0cdcf780 {
  display: flex;
  justify-content: space-between;
  margin-top: 52rpx;
}
.page .main .main_item_already .btnbox .btn.data-v-0cdcf780 {
  width: 294rpx;
  height: 82rpx;
  line-height: 82rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #2E80FE;
}
.page .main .main_item_already .btnbox .can.data-v-0cdcf780 {
  border: 2rpx solid #2E80FE;
}
.page .main .main_item_already .btnbox .re.data-v-0cdcf780 {
  background: #2E80FE;
  color: #FFFFFF;
}
.page .main .loading-status.data-v-0cdcf780 {
  text-align: center;
  padding: 40rpx 0;
  color: #999999;
  font-size: 28rpx;
}
.page .main .empty-data.data-v-0cdcf780 {
  text-align: center;
  padding: 200rpx 0;
  color: #999999;
  font-size: 32rpx;
}

