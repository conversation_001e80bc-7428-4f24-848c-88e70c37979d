<view class="page data-v-10e95c19"><view class="content data-v-10e95c19"><view class="card data-v-10e95c19"><view class="bottom data-v-10e95c19"><view class="left data-v-10e95c19">已选：</view><view class="right data-v-10e95c19"><view class="data-v-10e95c19">{{''+yikoujiaprice+''}}</view><block wx:for="{{chooseArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tag data-v-10e95c19">{{item.name}}</view></block><block wx:for="{{chosenInputValues}}" wx:for-item="item" wx:for-index="index"><view class="tag data-v-10e95c19">{{''+item.problemDesc+": "+item.val+''}}</view></block></view></view></view><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="chol data-v-10e95c19"><view class="choose data-v-10e95c19"><view class="title data-v-10e95c19"><block wx:if="{{item.isRequired==1}}"><label class="_span data-v-10e95c19">*</label></block>{{item.problemDesc}}</view><view class="desc data-v-10e95c19">{{item.problemContent}}</view><view class="cho_box data-v-10e95c19"><block wx:for="{{item.options}}" wx:for-item="newItem" wx:for-index="newIndex" wx:key="newIndex"><view data-event-opts="{{[['tap',[['chooseOne',[index,newIndex,'$0'],[[['list','',index,'inputType']]]]]]]}}" class="box_item data-v-10e95c19" style="{{(newItem.choose?'border:2rpx solid #2E80FE;color: #2E80FE;':'')}}" bindtap="__e">{{''+newItem.name+''}}<view class="ok data-v-10e95c19" style="{{(newItem.choose?'':'display:none;')}}"><uni-icons vue-id="{{'0c42a80d-1-'+index+'-'+newIndex}}" type="checkmarkempty" size="8" color="#fff" class="data-v-10e95c19" bind:__l="__l"></uni-icons></view></view></block></view></view><view class="fg data-v-10e95c19"></view></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="chol data-v-10e95c19"><view class="choose data-v-10e95c19"><view class="title data-v-10e95c19"><block wx:if="{{item.$orig.isRequired==1}}"><label class="_span data-v-10e95c19">*</label></block>{{item.$orig.problemDesc}}</view><view class="desc data-v-10e95c19">{{item.$orig.problemContent}}</view><view class="input-container data-v-10e95c19" id="{{'input-container-'+index}}"><input class="form-input data-v-10e95c19" type="text" placeholder="{{'请输入'+item.$orig.problemDesc}}" cursor-spacing="10" confirm-type="done" adjust-position="{{false}}" auto-height="{{false}}" data-event-opts="{{[['focus',[['handleInputFocus',[index]]]],['blur',[['handleInputBlur',['$event']]]],['input',[['__set_model',['$0','val','$event',[]],['form.data.'+item.m1+'']],['handleInput',['$event']]]]]}}" value="{{form.data[item.m2].val}}" bindfocus="__e" bindblur="__e" bindinput="__e"/></view></view><view class="fg data-v-10e95c19"></view></view></block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="chol data-v-10e95c19"><view class="choose data-v-10e95c19"><view class="title data-v-10e95c19"><block wx:if="{{item.$orig.isRequired==1}}"><label class="_span data-v-10e95c19">*</label></block>{{item.$orig.problemDesc}}</view><view class="desc up data-v-10e95c19">{{item.$orig.problemContent}}</view><upload vue-id="{{'0c42a80d-2-'+index}}" imagelist="{{form.data[item.m3].val}}" imgtype="{{item.m4}}" text="上传图片" imgsize="{{3}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" class="data-v-10e95c19" bind:__l="__l"></upload></view><view class="fg data-v-10e95c19"></view></view></block><view style="height:300rpx;" class="data-v-10e95c19"></view></view><view class="footer data-v-10e95c19" style="{{(footerStyle)}}"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="{{['righ','data-v-10e95c19',(isSubmitting)?'submitting':'']}}" bindtap="__e">{{''+(isSubmitting?'提交中...':'保存')+''}}</view></view></view>