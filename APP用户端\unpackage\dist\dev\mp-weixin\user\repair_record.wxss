@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-75ed86e6 {
  background-color: #F8F8F8;
  height: 100vh;
  overflow: auto;
  padding: 40rpx 30rpx;
}
.page .record_item.data-v-75ed86e6 {
  margin-bottom: 20rpx;
  width: 690rpx;
  height: 196rpx;
  background: #FFFFFF;
  border-radius: 10rpx 18rpx 18rpx 10rpx;
  display: flex;
  overflow: hidden;
}
.page .record_item .left.data-v-75ed86e6 {
  width: 10rpx;
  height: 100%;
  background-color: #2E80FE;
}
.page .record_item .mid.data-v-75ed86e6 {
  width: 606rpx;
  margin-left: 20rpx;
  padding-top: 20rpx;
}
.page .record_item .mid .title.data-v-75ed86e6 {
  max-width: 500rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .record_item .mid .ctx.data-v-75ed86e6 {
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  margin-top: 12rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #333333;
}
.page .record_item .mid .ctx ._span.data-v-75ed86e6 {
  display: inline-block;
  width: 40rpx;
}
.page .record_item .mid .address.data-v-75ed86e6 {
  margin-top: 42rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
  display: flex;
  align-content: center;
}
.page .record_item .mid .address text.data-v-75ed86e6 {
  margin-left: 8rpx;
}
.page .record_item .right.data-v-75ed86e6 {
  height: 100%;
  display: flex;
  align-items: center;
}
.page .record_item .right image.data-v-75ed86e6 {
  width: 40rpx;
  height: 40rpx;
}

