<view class="container"><view class="header-decoration"><view class="decoration-circle circle-1"></view><view class="decoration-circle circle-2"></view><view class="decoration-circle circle-3"></view></view><view class="user-info"><view class="status-section"><view class="status-container"><view class="status-info"><text class="status-label">开启接单</text><text class="status-desc">关闭后不可接收订单消息通知</text></view><u-switch vue-id="527e3c52-1" active-color="#599eff" value="{{messagePush}}" data-event-opts="{{[['^change',[['change']]],['^input',[['__set_model',['','messagePush','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l"></u-switch></view></view><view class="button-group"><button data-event-opts="{{[['tap',[['set',['$event']]]]]}}" class="action-button secondary-button" bindtap="__e"><text class="button-icon">️</text><text class="button-text">系统设置</text></button></view></view><u-modal vue-id="527e3c52-2" show="{{showSubscribeModal}}" title="提示" content="您已关闭消息订阅，建议点击‘通知管理’开启，方便及时接收师傅的报价通知。" showCancelButton="{{true}}" cancelText="取消" confirmText="去开启" data-event-opts="{{[['^confirm',[['goToSubscriptionSettings']]],['^cancel',[['e0']]]]}}" bind:confirm="__e" bind:cancel="__e" bind:__l="__l"></u-modal></view>