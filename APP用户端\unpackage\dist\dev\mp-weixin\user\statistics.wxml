<view class="page data-v-19ccd549"><view class="header data-v-19ccd549"><view class="head data-v-19ccd549"><view class="left data-v-19ccd549"><image src="{{userInfo.avatarUrl?userInfo.avatarUrl:'/static/mine/default_user.png'}}" mode class="data-v-19ccd549"></image><view class="name data-v-19ccd549">{{userInfo.nickName?userInfo.nickName:userInfo.phone||''}}</view></view><view class="right data-v-19ccd549">{{"已邀请"+(userNum+shiFuNum)}}</view></view></view><view class="tabs data-v-19ccd549"><view data-event-opts="{{[['tap',[['switchTab',[1]]]]]}}" class="{{['tab_item','data-v-19ccd549',(currentTab===1)?'active':'']}}" bindtap="__e">{{"邀请的用户 "+userNum}}</view><view data-event-opts="{{[['tap',[['switchTab',[2]]]]]}}" class="{{['tab_item','data-v-19ccd549',(currentTab===2)?'active':'']}}" bindtap="__e">{{"邀请的师傅"+shiFuNum}}</view></view><view class="fg data-v-19ccd549"></view><view class="box data-v-19ccd549"><view class="title data-v-19ccd549">我邀请的</view><view class="list data-v-19ccd549"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-19ccd549"><view class="list_item data-v-19ccd549" style="display:flex;justify-content:space-between;align-items:center;"><view style="display:flex;justify-content:space-between;align-items:center;" class="data-v-19ccd549"><image src="{{item.$orig.avatarUrl?item.$orig.avatarUrl:'/static/mine/default_user.png'}}" mode class="data-v-19ccd549"></image><view class="info data-v-19ccd549"><view class="nam data-v-19ccd549">{{item.$orig.nickName}}</view><view class="nam data-v-19ccd549">{{item.$orig.phone}}</view></view></view><view class="data-v-19ccd549"><view style="display:flex;justify-content:center;align-items:center;" class="data-v-19ccd549">{{''+(item.$orig.shifu===0?"用户":"师傅")+''}}<block wx:if="{{item.$orig.cnum>0}}"><text style="margin-left:30rpx;" class="data-v-19ccd549">{{"邀请"+item.$orig.cnum+"人"}}</text></block></view><view style="font-size:24rpx;" class="data-v-19ccd549">{{''+item.$orig.createTime+''}}</view><block wx:if="{{item.g0}}"><view data-event-opts="{{[['tap',[['toggleExpand',['$0'],[[['list','',index]]]]]]]}}" class="expand-toggle data-v-19ccd549" bindtap="__e">{{''+(item.$orig.expanded?'折叠':'展开')+''}}</view></block></view></view><block wx:if="{{item.g1}}"><view class="children-list data-v-19ccd549"><block wx:for="{{item.$orig.children}}" wx:for-item="child" wx:for-index="childIndex" wx:key="childIndex"><view class="child-item data-v-19ccd549"><view style="display:flex;justify-content:space-between;align-items:center;" class="data-v-19ccd549"><view style="display:flex;justify-content:space-between;align-items:center;" class="data-v-19ccd549"><image src="{{child.avatarUrl?child.avatarUrl:'/static/mine/default_user.png'}}" mode class="data-v-19ccd549"></image><view class="info data-v-19ccd549"><view class="nam data-v-19ccd549">{{child.nickName}}</view><view class="nam data-v-19ccd549">{{child.phone}}</view></view></view><view class="data-v-19ccd549"><view style="display:flex;justify-content:center;align-items:center;" class="data-v-19ccd549">{{''+(child.shifu===0?"用户":"师傅")+''}}</view><view style="font-size:24rpx;margin-left:30rpx;" class="data-v-19ccd549">{{''+child.createTime+''}}</view></view></view></view></block></view></block></block></block></view><block wx:if="{{$root.g2<total}}"><view data-event-opts="{{[['tap',[['loadMore',['$event']]]]]}}" class="load-more data-v-19ccd549" bindtap="__e">加载更多</view></block></view></view>