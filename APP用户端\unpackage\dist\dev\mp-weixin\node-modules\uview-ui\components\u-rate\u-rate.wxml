<view class="u-rate data-v-01de4127 vue-ref" style="{{$root.s0}}" id="{{elId}}" data-ref="u-rate"><view data-event-opts="{{[['touchmove',[['touchMove',['$event']]]],['touchend',[['touchEnd',['$event']]]]]}}" class="u-rate__content data-v-01de4127" catchtouchmove="__e" catchtouchend="__e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['u-rate__content__item','data-v-01de4127',elClass]}}"><view data-ref="u-rate__content__item__icon-wrap" data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({index})}}" class="u-rate__content__item__icon-wrap data-v-01de4127 vue-ref-in-for" catchtap="__e"><u-icon vue-id="{{'480eec0d-1-'+index}}" name="{{$root.g0>index?activeIcon:inactiveIcon}}" color="{{disabled?'#c8c9cc':$root.g1>index?activeColor:inactiveColor}}" custom-style="{{item.a0}}" size="{{size}}" class="data-v-01de4127" bind:__l="__l"></u-icon></view><block wx:if="{{allowHalf}}"><view data-ref="u-rate__content__item__icon-wrap" data-event-opts="{{[['tap',[['e1',['$event']]]]]}}" data-event-params="{{({index})}}" class="u-rate__content__item__icon-wrap u-rate__content__item__icon-wrap--half data-v-01de4127 vue-ref-in-for" style="{{'width:'+($root.g2)+';'}}" catchtap="__e"><u-icon vue-id="{{'480eec0d-2-'+index}}" name="{{$root.g3>index?activeIcon:inactiveIcon}}" color="{{disabled?'#c8c9cc':$root.g4>index?activeColor:inactiveColor}}" custom-style="{{item.a1}}" size="{{size}}" class="data-v-01de4127" bind:__l="__l"></u-icon></view></block></view></block></view></view>