@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page .header.data-v-4250a084 {
  border-top: 2rpx solid #f3f3f3;
  border-bottom: 2rpx solid #f3f3f3;
  padding: 36rpx 30rpx;
}
.page .header .title.data-v-4250a084 {
  margin-bottom: 20rpx;
}
.page .mid.data-v-4250a084 {
  padding: 30rpx;
}
.page .mid textarea.data-v-4250a084 {
  margin-bottom: 20rpx;
}
.page .btn.data-v-4250a084 {
  width: 686rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  line-height: 98rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  position: absolute;
  bottom: 42rpx;
  left: 30rpx;
}

