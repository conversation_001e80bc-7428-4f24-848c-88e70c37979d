<view data-event-opts="{{[['tap',[['wrapperClickHandler',['$event']]]]]}}" class="{{['u-checkbox','data-v-532d01c7','u-checkbox-label--'+parentData.iconPlacement,parentData.borderBottom&&parentData.placement==='column'&&'u-border-bottom']}}" style="{{$root.s0}}" catchtap="__e"><view data-event-opts="{{[['tap',[['iconClickHandler',['$event']]]]]}}" class="{{['u-checkbox__icon-wrap','data-v-532d01c7',iconClasses]}}" style="{{$root.s1}}" catchtap="__e"><block wx:if="{{$slots.icon}}"><slot name="icon"></slot></block><block wx:else><u-icon class="u-checkbox__icon-wrap__icon data-v-532d01c7" vue-id="0009dd26-1" name="checkbox-mark" size="{{elIconSize}}" color="{{elIconColor}}" bind:__l="__l"></u-icon></block></view><text data-event-opts="{{[['tap',[['labelClickHandler',['$event']]]]]}}" style="{{'color:'+(elDisabled?elInactiveColor:elLabelColor)+';'+('font-size:'+(elLabelSize)+';')+('line-height:'+(elLabelSize)+';')}}" catchtap="__e" class="data-v-532d01c7">{{label}}</text></view>