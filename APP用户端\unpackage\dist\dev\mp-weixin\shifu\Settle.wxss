@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-458b6785 {
  padding-bottom: 200rpx;
}
.page .header.data-v-458b6785 {
  width: 750rpx;
  height: 58rpx;
  background: #fff7f1;
  line-height: 58rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 400;
}
.page .main.data-v-458b6785 {
  padding: 40rpx 30rpx;
}
.page .main .main_item.data-v-458b6785 {
  margin-bottom: 20rpx;
}
.page .main .main_item .title.data-v-458b6785 {
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
}
.page .main .main_item .title ._span.data-v-458b6785 {
  color: #e72427;
}
.page .main .main_item input.data-v-458b6785 {
  width: 690rpx;
  height: 110rpx;
  background: #f8f8f8;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 110rpx;
  padding: 0 40rpx;
  box-sizing: border-box;
}
.page .main .main_item input.data-v-458b6785:disabled {
  background: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}
.page .main .main_item .input-disabled.data-v-458b6785 {
  background: #f8f8f8 !important;
  color: #333 !important;
  cursor: default !important;
}
.page .main .main_item .card.data-v-458b6785 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.page .main .main_item .card .card_item.data-v-458b6785 {
  width: 332rpx;
  height: 332rpx;
  background: #f2fafe;
  border-radius: 16rpx;
  overflow: hidden;
}
.page .main .main_item .card .card_item .top.data-v-458b6785 {
  height: 266rpx;
  width: 100%;
  padding-top: 40rpx;
}
.page .main .main_item .card .card_item .top .das.data-v-458b6785 {
  margin: 0 auto;
  width: 266rpx;
  height: 180rpx;
  border: 2rpx dashed #2e80fe;
  padding-top: 28rpx;
}
.page .main .main_item .card .card_item .top .das .up.data-v-458b6785 {
  margin: 0 auto;
  width: 210rpx;
  height: 130rpx;
}
.page .main .main_item .card .card_item .bottom.data-v-458b6785 {
  height: 66rpx;
  width: 100%;
  background-color: #2e80fe;
  font-size: 28rpx;
  font-weight: 400;
  color: #ffffff;
  text-align: center;
  line-height: 66rpx;
}
.page .main .main_item .disabled-upload.data-v-458b6785 {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 8rpx;
}
.page .main .main_item .disabled-upload .image-preview.data-v-458b6785 {
  width: 100%;
  height: 100%;
}
.page .main .main_item .disabled-upload .image-preview image.data-v-458b6785 {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.page .main .main_item .disabled-upload .upload-placeholder.data-v-458b6785 {
  color: #999;
  font-size: 24rpx;
  text-align: center;
}
.page .main .main_item .disabled-upload-list.data-v-458b6785 {
  display: flex;
  justify-content: center;
  width: 100%;
}
.page .main .main_item .disabled-upload-list .image-list.data-v-458b6785 {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  justify-content: center;
}
.page .main .main_item .disabled-upload-list .image-list .image-item.data-v-458b6785 {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  overflow: hidden;
}
.page .main .main_item .disabled-upload-list .image-list .image-item image.data-v-458b6785 {
  width: 100%;
  height: 100%;
}
.page .main .main_item .disabled-upload-list .upload-placeholder.data-v-458b6785 {
  width: 200rpx;
  height: 200rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
}
.page .footer.data-v-458b6785 {
  padding: 52rpx 30rpx;
  width: 750rpx;
  background: #ffffff;
  border-top: 1rpx solid #e8e8e8;
  position: fixed;
  bottom: 0;
  z-index: 999;
}
.page .footer .btn.data-v-458b6785 {
  width: 690rpx;
  height: 98rpx;
  background: #2e80fe;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 98rpx;
  text-align: center;
}

