@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-988c8adc {
  padding: 40rpx 32rpx;
  min-height: 100vh;
  overflow: auto;
  background: #F8F8F8;
}
.page .card_item.data-v-988c8adc {
  width: 686rpx;
  height: 250rpx;
  background: linear-gradient(270deg, #34538D 0%, #4E89B7 100%);
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  margin-bottom: 20rpx;
  padding: 0 40rpx;
  padding-top: 56rpx;
  position: relative;
}
.page .card_item .title.data-v-988c8adc {
  font-size: 36rpx;
  font-weight: 500;
  color: #FFFFFF;
}
.page .card_item .num.data-v-988c8adc {
  font-size: 36rpx;
  font-weight: 500;
  color: #FFFFFF;
  margin-top: 62rpx;
}
.page .card_item .trash.data-v-988c8adc {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
}
.page .add.data-v-988c8adc {
  width: 686rpx;
  height: 102rpx;
  background: #FFFFFF;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  opacity: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
}
.page .add .left.data-v-988c8adc {
  display: flex;
  align-items: center;
}
.page .add .left image.data-v-988c8adc {
  width: 41rpx;
  height: 41rpx;
}
.page .add .left text.data-v-988c8adc {
  margin-left: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

