<view class="page data-v-23563916"><tabbar vue-id="47716ab0-1" cur="{{0}}" class="data-v-23563916" bind:__l="__l"></tabbar><view class="img data-v-23563916"><u-swiper vue-id="47716ab0-2" list="{{list1}}" height="108" class="data-v-23563916" bind:__l="__l"></u-swiper></view><view class="location-bar data-v-23563916"><view class="location-info data-v-23563916"><view class="data-v-23563916">{{"当前接单位置："+(province+city+district||'定位中...')}}</view></view></view><view class="check_box data-v-23563916"><view class="check data-v-23563916"><u-tag vue-id="47716ab0-3" text="{{currentCateName}}" icon="arrow-down-fill" data-event-opts="{{[['^click',[['chooseCate']]]]}}" bind:click="__e" class="data-v-23563916" bind:__l="__l"></u-tag><block wx:if="{{showCate}}"><view class="cate-dropdown data-v-23563916"><block wx:for="{{cateList}}" wx:for-item="cate" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectClick',['$0'],[[['cateList','',index]]]]]]]}}" class="cate-item data-v-23563916" bindtap="__e">{{''+cate.name+''}}</view></block></view></block></view><view class="reset data-v-23563916"><u-tag vue-id="47716ab0-4" text="重置筛选" plain="{{true}}" plain-fill="{{true}}" data-event-opts="{{[['^click',[['reset']]]]}}" bind:click="__e" class="data-v-23563916" bind:__l="__l"></u-tag></view></view><block wx:if="{{$root.g0==0}}"><u-empty vue-id="47716ab0-5" mode="order" icon="http://cdn.uviewui.com/uview/empty/order.png" class="data-v-23563916" bind:__l="__l"></u-empty></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['seeDetail',['$0'],[[['list','',index]]]]]]]}}" class="re_item data-v-23563916" bindtap="__e"><view class="top data-v-23563916"><image style="width:160rpx;height:160rpx;border-radius:10rpx;" src="{{item.$orig.goodsCover}}" class="data-v-23563916"></image><view class="order data-v-23563916"><view class="title _div data-v-23563916">{{item.$orig.goodsName}}<block wx:if="{{item.$orig.type!=0}}"><label style="font-size:24rpx;color:#999;margin-left:10rpx;" class="_span data-v-23563916">(报价0.00元起)</label></block></view><view class="price _div data-v-23563916">{{item.$orig.type==0?'￥'+item.$orig.payPrice:'待报价'}}</view></view></view><view data-event-opts="{{[['tap',[['dingyue']]]]}}" class="info data-v-23563916" bindtap="__e"><view class="address data-v-23563916"><view class="left data-v-23563916"><u-icon vue-id="{{'47716ab0-6-'+index}}" name="map-fill" color="#2979ff" size="22" class="data-v-23563916" bind:__l="__l"></u-icon></view><view class="right data-v-23563916"><view class="address_name data-v-23563916">{{item.$orig.address}}</view><view class="address_Info data-v-23563916">{{item.$orig.addressInfo}}</view></view></view><view class="tel data-v-23563916"><view class="left data-v-23563916"><u-icon vue-id="{{'47716ab0-7-'+index}}" name="phone-fill" color="#2979ff" size="22" class="data-v-23563916" bind:__l="__l"></u-icon></view><view class="right data-v-23563916">{{item.g1+'********'}}</view></view></view><block wx:if="{{item.$orig.text!=''}}"><view class="notes data-v-23563916"><view style="color:#999999;" class="data-v-23563916">备注内容:</view>{{''+item.$orig.text+''}}</view></block><view data-event-opts="{{[['tap',[['seeDetail',['$0'],[[['list','',index]]]]]]]}}" class="btn data-v-23563916" style="{{(item.$orig.type==1?'':'background-color:#2E80FE;color:#fff;')}}" catchtap="__e">{{''+(item.$orig.type==1?'立即报价':'立即接单')+''}}</view></view></block><u-popup vue-id="47716ab0-8" show="{{show}}" round="{{10}}" closeable="{{true}}" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-23563916" bind:__l="__l" vue-slots="{{['default']}}"><view class="box data-v-23563916"><view class="title data-v-23563916">立即报价</view><view class="title2 data-v-23563916">报价金额</view><view class="money data-v-23563916"><u--input vue-id="{{('47716ab0-9')+','+('47716ab0-8')}}" placeholder="请输入报价金额" prefixIcon="rmb" prefixIconStyle="font-size: 22px;color: #909399" type="digit" maxlength="10" value="{{input}}" data-event-opts="{{[['^input',[['__set_model',['','input','$event',[]]],['validateInput']]]]}}" bind:input="__e" class="data-v-23563916" bind:__l="__l"></u--input></view><view data-event-opts="{{[['tap',[['confirmBao',['$event']]]]]}}" class="btn data-v-23563916" bindtap="__e">确认报价</view></view></u-popup><u-modal vue-id="47716ab0-10" show="{{confirmshow}}" content="{{content}}" showCancelButton="{{true}}" data-event-opts="{{[['^confirm',[['confirmRe']]],['^cancel',[['e0']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-23563916" bind:__l="__l"></u-modal><u-modal vue-id="47716ab0-11" show="{{masterModalShow}}" content="您还不是师傅,请去入驻" showCancelButton="{{true}}" data-event-opts="{{[['^confirm',[['goToSettle']]],['^cancel',[['e1']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-23563916" bind:__l="__l"></u-modal><block wx:if="{{shifustutus.data!==-2&&shifustutus.data!==-1}}"><u-modal vue-id="47716ab0-12" show="{{detailModalShow}}" title="服务承诺" showCancelButton="{{true}}" cancelText="不同意" confirmText="同意" data-event-opts="{{[['^confirm',[['confirmDetail']]],['^cancel',[['e2']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-23563916" bind:__l="__l" vue-slots="{{['default']}}"><view class="modal-content data-v-23563916"><rich-text nodes="{{getconfigs?getconfigs:configInfo.shifuQualityCommitment}}" class="data-v-23563916"></rich-text></view></u-modal></block><block wx:if="{{$root.g2>=10}}"><view class="loadmore data-v-23563916"><u-loadmore vue-id="47716ab0-13" status="{{status}}" class="data-v-23563916" bind:__l="__l"></u-loadmore></view></block></view>