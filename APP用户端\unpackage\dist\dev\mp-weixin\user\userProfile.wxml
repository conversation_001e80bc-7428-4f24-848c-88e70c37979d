<view class="container"><view class="user-info"><view class="avatar-wrapper"><button class="choose-avatar-button" open-type="chooseAvatar" data-event-opts="{{[['chooseavatar',[['onChooseAvatar',['$event']]]]]}}" bindchooseavatar="__e"><image class="avatar" src="{{userInfo.avatarUrl||'/static/mine/default_user.png'}}" mode="aspectFill"></image></button></view><view class="nickname-section"><text class="label">昵称：</text><input class="nickname-input" type="nickname" placeholder="请输入昵称" data-event-opts="{{[['blur',[['onNickNameBlur',['$event']]]],['input',[['__set_model',['','localNickName','$event',[]]]]]]}}" value="{{localNickName}}" bindblur="__e" bindinput="__e"/></view><button data-event-opts="{{[['tap',[['saveUserInfo',['$event']]]]]}}" class="save-button" bindtap="__e">保存</button><button data-event-opts="{{[['tap',[['set',['$event']]]]]}}" class="save-button" bindtap="__e">系统设置</button><button data-event-opts="{{[['tap',[['userOut',['$event']]]]]}}" class="save-button" bindtap="__e">退出</button></view></view>