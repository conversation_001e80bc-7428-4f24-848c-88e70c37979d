@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* Your existing styles remain unchanged */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding-top: 40rpx;
  position: relative;
  overflow: hidden;
}
.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  pointer-events: none;
}
.header-decoration .decoration-circle {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
}
.header-decoration .decoration-circle.circle-1 {
  width: 120rpx;
  height: 120rpx;
  top: -60rpx;
  right: 100rpx;
  -webkit-animation: float 6s ease-in-out infinite;
          animation: float 6s ease-in-out infinite;
}
.header-decoration .decoration-circle.circle-2 {
  width: 80rpx;
  height: 80rpx;
  top: 50rpx;
  right: 300rpx;
  animation: float 4s ease-in-out infinite reverse;
}
.header-decoration .decoration-circle.circle-3 {
  width: 60rpx;
  height: 60rpx;
  top: 20rpx;
  left: 80rpx;
  -webkit-animation: float 5s ease-in-out infinite;
          animation: float 5s ease-in-out infinite;
}
@-webkit-keyframes float {
0%, 100% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
}
50% {
    -webkit-transform: translateY(-20rpx);
            transform: translateY(-20rpx);
}
}
@keyframes float {
0%, 100% {
    -webkit-transform: translateY(0px);
            transform: translateY(0px);
}
50% {
    -webkit-transform: translateY(-20rpx);
            transform: translateY(-20rpx);
}
}
.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  width: 90%;
  padding: 60rpx 40rpx;
  border-radius: 30rpx;
  margin-top: 80rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}
.user-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  border-radius: 30rpx 30rpx 0 0;
}
.status-section {
  width: 100%;
  margin-bottom: 60rpx;
}
.status-section .status-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f3ff 100%);
  border-radius: 20rpx;
  border: 2rpx solid rgba(89, 158, 255, 0.1);
}
.status-section .status-container .status-info {
  display: flex;
  flex-direction: column;
}
.status-section .status-container .status-info .status-label {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 8rpx;
}
.status-section .status-container .status-info .status-desc {
  font-size: 24rpx;
  color: #666;
}
.button-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.button-group .action-button {
  width: 100%;
  height: 100rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: none;
}
.button-group .action-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transition: all 0.3s ease;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}
.button-group .action-button:active::before {
  width: 200%;
  height: 200%;
}
.button-group .action-button .button-icon {
  margin-right: 15rpx;
  font-size: 28rpx;
}
.button-group .action-button .button-text {
  position: relative;
  z-index: 1;
}
.button-group .action-button.secondary-button {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
  color: #599eff;
  border: 2rpx solid rgba(89, 158, 255, 0.2);
  box-shadow: 0 5rpx 15rpx rgba(89, 158, 255, 0.1);
}
.button-group .action-button.secondary-button:active {
  -webkit-transform: translateY(2rpx);
          transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(89, 158, 255, 0.1);
}
/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.user-info {
    width: 95%;
    padding: 40rpx 30rpx;
}
}

