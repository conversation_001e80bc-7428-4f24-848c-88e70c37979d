@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-23563916 {
  min-height: 100vh;
  overflow: auto;
  background-color: #f3f4f5;
  padding-bottom: 120rpx;
}
.page .img.data-v-23563916 {
  width: 690rpx;
  margin: 20rpx auto;
}
.page .location-bar.data-v-23563916 {
  display: flex;
  padding: 20rpx;
  border: 1rpx solid #eeeeee;
  color: #999;
  font-size: 28rpx;
}
.page .subscription.data-v-23563916 {
  flex-shrink: 0;
}
.page .location-info.data-v-23563916 {
  margin-left: auto;
  text-align: right;
}
.page .check_box.data-v-23563916 {
  margin: 20rpx auto;
  width: 690rpx;
  display: flex;
  justify-content: space-between;
  position: relative;
}
.page .check_box .check.data-v-23563916 {
  width: 500rpx;
  position: relative;
}
.page .check_box .reset.data-v-23563916 {
  width: 160rpx;
}
.page .check_box .cate-dropdown.data-v-23563916 {
  position: absolute;
  top: 80rpx;
  left: 0;
  width: 500rpx;
  max-height: 400rpx;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 10rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
}
.page .check_box .cate-item.data-v-23563916 {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}
.page .check_box .cate-item.data-v-23563916:last-child {
  border-bottom: none;
}
.page .check_box .cate-item.data-v-23563916:hover {
  background-color: #f8f8f8;
}
.page .box.data-v-23563916 {
  padding: 40rpx 30rpx;
}
.page .box .title.data-v-23563916 {
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .box .title2.data-v-23563916 {
  margin-top: 32rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #171717;
}
.page .box .btn.data-v-23563916 {
  margin: 0 auto;
  margin-top: 42rpx;
  width: 688rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 12rpx;
  line-height: 98rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
}
.page .modal-content.data-v-23563916 {
  padding: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
}
.page .re_item.data-v-23563916 {
  width: 690rpx;
  background-color: #fff;
  margin: 20rpx auto;
  padding: 40rpx;
}
.page .re_item .top.data-v-23563916 {
  display: flex;
}
.page .re_item .top image.data-v-23563916 {
  margin-right: 20rpx;
}
.page .re_item .top .order.data-v-23563916 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.page .re_item .top .order .title.data-v-23563916 {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .re_item .top .order .price.data-v-23563916 {
  font-size: 28rpx;
  font-weight: 500;
  color: #2E80FE;
}
.page .re_item .info.data-v-23563916 {
  margin-top: 40rpx;
}
.page .re_item .info .address.data-v-23563916 {
  display: flex;
  align-items: center;
}
.page .re_item .info .address .left.data-v-23563916 {
  margin-right: 20rpx;
}
.page .re_item .info .address .right .address_name.data-v-23563916 {
  font-size: 40rpx;
  font-weight: 500;
  color: #333333;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .re_item .info .address .right .address_info.data-v-23563916 {
  margin-top: 12rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .re_item .info .tel.data-v-23563916 {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
}
.page .re_item .info .tel .left.data-v-23563916 {
  margin-right: 20rpx;
}
.page .re_item .info .tel .right.data-v-23563916 {
  font-size: 40rpx;
  font-weight: 500;
  color: #333333;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .re_item .notes.data-v-23563916 {
  background-color: #f2f3f4;
  border-radius: 5rpx;
  padding: 10rpx;
}
.page .re_item .btn.data-v-23563916 {
  margin: 0 auto;
  margin-top: 40rpx;
  width: 610rpx;
  height: 82rpx;
  border-radius: 12rpx;
  border: 2rpx solid #2E80FE;
  line-height: 82rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #2E80FE;
}
.page .loadmore.data-v-23563916 {
  display: flex;
  justify-content: center;
}
.page .footer.data-v-23563916 {
  color: #333;
  margin: 20rpx 0;
  text-align: center;
  font-size: 24rpx;
}

