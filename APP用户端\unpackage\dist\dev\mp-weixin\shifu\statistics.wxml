<view class="page data-v-921da386"><view class="header data-v-921da386"><view class="head data-v-921da386"><view class="left data-v-921da386"><image src="{{userInfo.avatarUrl?userInfo.avatarUrl:'/static/mine/default_user.png'}}" mode class="data-v-921da386"></image><view class="name data-v-921da386">{{userInfo.nickName||''}}</view></view><view class="right data-v-921da386">{{"已邀请"+total}}</view></view></view><view class="fg data-v-921da386"></view><view class="box data-v-921da386"><view class="title data-v-921da386">我邀请的</view><view class="list data-v-921da386"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list_item data-v-921da386" style="display:flex;justify-content:space-between;align-items:center;"><view style="display:flex;justify-content:space-between;align-items:center;" class="data-v-921da386"><image src="{{item.avatarUrl?item.avatarUrl:'/static/mine/default_user.png'}}" mode class="data-v-921da386"></image><view class="info data-v-921da386"><view class="nam data-v-921da386">{{item.nickName}}</view><view class="nam data-v-921da386">{{item.phone}}</view></view></view><view class="data-v-921da386"><view class="data-v-921da386"><view style="display:flex;justify-content:center;align-items:center;" class="data-v-921da386">{{''+(item.shifu===0?"用户":"师傅")+''}}</view><view style="font-size:24rpx;" class="data-v-921da386">{{''+item.createTime+''}}</view></view></view></view></block></view><block wx:if="{{$root.g0<total}}"><view data-event-opts="{{[['tap',[['loadMore',['$event']]]]]}}" class="load-more data-v-921da386" bindtap="__e">加载更多</view></block></view></view>