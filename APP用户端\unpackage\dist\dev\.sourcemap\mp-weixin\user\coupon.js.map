{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/coupon.vue?e5f5", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/coupon.vue?7dea", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/coupon.vue?bf32", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/coupon.vue?ac67", "uni-app:///user/coupon.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/coupon.vue?2d80", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP用户端/user/coupon.vue?9e74"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentIndex", "list", "name", "arr", "couponlist", "page", "pageSize", "total", "loadingStatus", "loadingText", "hasMore", "isRefreshing", "isLoadingMore", "mainHeight", "onLoad", "uni", "success", "onPullDownRefresh", "console", "onReachBottom", "methods", "changeTab", "goUrl", "url", "loadInitialData", "refreshData", "loadMoreData", "getList", "<PERSON><PERSON><PERSON><PERSON>", "status", "serviceId", "pageNum", "then", "item", "type", "full", "discount", "title", "startTime", "day", "rule", "newList", "setTimeout", "resolve", "catch", "icon", "reject", "watch"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAq1B,CAAgB,q2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCwEz2B;EACAC;IACA;MACAC;MACAC,OACA;QAAAC;MAAA,GACA;QAAAA;MAAA,GACA;QAAAA;MAAA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACAC;MACAC;QACA;QACA;QACA;MACA;IACA;IACA;EACA;EACAC;IACAC;IACA;EACA;EACAC;IACAD;IACA;MACA;MACA;IACA;EACA;EACAE;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;MACAP;QACAQ;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACAV;MACA;IACA;IACAW;MAAA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;MACA;QACA;QACA;QACA,oBACAC;UACAC;UACAC;UACAC;UACAzB;QACA,GACA0B;UACAd;UACA;UACA;UACA;YAAA,uCACAe;cACA;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;cACAC;YAAA;UAAA,CACA;UACA;YACA;UACA;YACA;UACA;UACA;UACA,iBACA,2CACAC;UACA;YACA;YACA;YACAC;cACA;YACA;UACA;UACAC;QACA,GACAC;UACA1B;UACA;YACA;UACA;;UACA,eACA;UACAH;YACA8B;YACAR;UACA;UACAS;QACA;MACA;IACA;EACA;EACAC;IACA/C;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxOA;AAAA;AAAA;AAAA;AAA4lD,CAAgB,gjDAAG,EAAC,C;;;;;;;;;;;ACAhnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "user/coupon.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './user/coupon.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./coupon.vue?vue&type=template&id=423579cc&scoped=true&\"\nvar renderjs\nimport script from \"./coupon.vue?vue&type=script&lang=js&\"\nexport * from \"./coupon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./coupon.vue?vue&type=style&index=0&id=423579cc&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"423579cc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"user/coupon.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupon.vue?vue&type=template&id=423579cc&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.couponlist.length === 0 && !_vm.loadingStatus\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupon.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t<view class=\"header\">\n\t\t\t<view\n\t\t\t\tclass=\"header-item\"\n\t\t\t\tv-for=\"(item, index) in list\"\n\t\t\t\t:key=\"index\"\n\t\t\t\t:style=\"currentIndex === index ? 'color:#2E80FE;' : ''\"\n\t\t\t\t@tap=\"changeTab(index)\"\n\t\t\t>\n\t\t\t\t{{ item.name }}\n\t\t\t\t<view\n\t\t\t\t\tclass=\"tag\"\n\t\t\t\t\t:style=\"currentIndex === index ? '' : 'background-color:#fff;'\"\n\t\t\t\t></view>\n\t\t\t</view>\n\t\t</view>\n\t\t<scroll-view\n\t\t\tclass=\"main\"\n\t\t\tscroll-y\n\t\t\t:style=\"{ height: mainHeight }\"\n\t\t\t@scrolltolower=\"onReachBottom\"\n\t\t>\n\t\t\t<view\n\t\t\t\tclass=\"main_item\"\n\t\t\t\tv-for=\"(item, index) in couponlist\"\n\t\t\t\t:key=\"index\"\n\t\t\t>\n\t\t\t\t<view class=\"top\">\n\t\t\t\t\t<view class=\"box1\" v-if=\"item.type === 0\">\n\t\t\t\t\t\t<span>满</span>{{ item.full }}<span>减</span>{{ item.discount }}\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"box1\" v-else><span>￥</span>{{ item.discount }}</view>\n\t\t\t\t\t<view class=\"box2\">\n\t\t\t\t\t\t<text>{{ item.title }}</text>\n\t\t\t\t\t\t<span v-if=\"item.startTime === 0\"\n\t\t\t\t\t\t\t>有效期：自领券日起{{ item.day }}天</span\n\t\t\t\t\t\t>\n\t\t\t\t\t\t<span v-else>有效期：{{ item.startTime }}</span>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"box3\"\n\t\t\t\t\t\t@tap=\"goUrl\"\n\t\t\t\t\t\t:style=\"\n\t\t\t\t\t\t\tcurrentIndex === 0\n\t\t\t\t\t\t\t\t? ''\n\t\t\t\t\t\t\t\t: 'background-color:#f2f3f4;color:#999;'\n\t\t\t\t\t\t\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{{ arr[currentIndex] }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"bottom\">\n\t\t\t\t\t{{ item.rule }}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!-- 加载状态提示 -->\n\t\t\t<view class=\"loading-status\" v-if=\"loadingStatus\">\n\t\t\t\t<text>{{ loadingText }}</text>\n\t\t\t</view>\n\t\t\t<!-- 空数据提示 -->\n\t\t\t<view\n\t\t\t\tclass=\"empty-data\"\n\t\t\t\tv-if=\"couponlist.length === 0 && !loadingStatus\"\n\t\t\t>\n\t\t\t\t<text>暂无数据</text>\n\t\t\t</view>\n\t\t</scroll-view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tcurrentIndex: 0,\n\t\t\tlist: [\n\t\t\t\t{ name: '待使用' },\n\t\t\t\t{ name: '已使用' },\n\t\t\t\t{ name: '已过期' },\n\t\t\t],\n\t\t\tarr: ['去使用', '已使用', '已过期'],\n\t\t\tcouponlist: [],\n\t\t\tpage: 1,\n\t\t\tpageSize: 50,\n\t\t\ttotal: 0,\n\t\t\tloadingStatus: false,\n\t\t\tloadingText: '',\n\t\t\thasMore: true,\n\t\t\tisRefreshing: false,\n\t\t\tisLoadingMore: false,\n\t\t\tmainHeight: '0px', // Dynamic height for scroll-view\n\t\t};\n\t},\n\tonLoad() {\n\t\t// Set scroll-view height dynamically\n\t\tuni.getSystemInfo({\n\t\t\tsuccess: (res) => {\n\t\t\t\tconst windowHeight = res.windowHeight;\n\t\t\t\tconst headerHeight = 50; // Approximate header height in pixels\n\t\t\t\tthis.mainHeight = `${windowHeight - headerHeight}px`;\n\t\t\t},\n\t\t});\n\t\tthis.loadInitialData();\n\t},\n\tonPullDownRefresh() {\n\t\tconsole.log('Pull down refresh triggered');\n\t\tthis.refreshData();\n\t},\n\tonReachBottom() {\n\t\tconsole.log('Reached bottom, loading more...');\n\t\tif (this.couponlist.length < this.total && !this.isLoadingMore) {\n\t\t\tthis.page += 1;\n\t\t\tthis.loadMoreData();\n\t\t}\n\t},\n\tmethods: {\n\t\tchangeTab(index) {\n\t\t\tif (this.currentIndex !== index) {\n\t\t\t\tthis.currentIndex = index;\n\t\t\t}\n\t\t},\n\t\tgoUrl() {\n\t\t\tif (this.currentIndex !== 0) return;\n\t\t\tuni.redirectTo({\n\t\t\t\turl: '/pages/technician',\n\t\t\t});\n\t\t},\n\t\tloadInitialData() {\n\t\t\tthis.loadingStatus = true;\n\t\t\tthis.loadingText = '正在加载...';\n\t\t\tthis.getList().finally(() => {\n\t\t\t\tthis.loadingStatus = false;\n\t\t\t});\n\t\t},\n\t\trefreshData() {\n\t\t\tif (this.isRefreshing) return;\n\t\t\tthis.isRefreshing = true;\n\t\t\tthis.page = 1;\n\t\t\tthis.couponlist = [];\n\t\t\tthis.hasMore = true;\n\t\t\tthis.loadingStatus = true;\n\t\t\tthis.loadingText = '正在刷新...';\n\t\t\tthis.getList(false).finally(() => {\n\t\t\t\tthis.isRefreshing = false;\n\t\t\t\tthis.loadingStatus = false;\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t});\n\t\t},\n\t\tloadMoreData() {\n\t\t\tif (this.isLoadingMore || !this.hasMore) return;\n\t\t\tthis.isLoadingMore = true;\n\t\t\tthis.loadingStatus = true;\n\t\t\tthis.loadingText = '正在加载更多...';\n\t\t\tthis.getList(true).finally(() => {\n\t\t\t\tthis.isLoadingMore = false;\n\t\t\t\tthis.loadingStatus = false;\n\t\t\t});\n\t\t},\n\t\tgetList(append = false) {\n\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\tconst status = this.currentIndex + 1;\n\t\t\t\t// Mock API call for demonstration (replace with your actual API)\n\t\t\t\tthis.$api.service\n\t\t\t\t\t.myWelfare({\n\t\t\t\t\t\tstatus: status,\n\t\t\t\t\t\tserviceId: -1,\n\t\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\t\tpageSize: this.pageSize,\n\t\t\t\t\t})\n\t\t\t\t\t.then((res) => {\n\t\t\t\t\t\tconsole.log('API Response:', res);\n\t\t\t\t\t\t// Ensure response structure is as expected\n\t\t\t\t\t\tthis.total = res.data.totalCount || 0;\n\t\t\t\t\t\tconst newList = (res.data.list || []).map((item) => ({\n\t\t\t\t\t\t\t...item,\n\t\t\t\t\t\t\t// Ensure all required fields are present\n\t\t\t\t\t\t\ttype: item.type || 0,\n\t\t\t\t\t\t\tfull: item.full || 0,\n\t\t\t\t\t\t\tdiscount: item.discount || 0,\n\t\t\t\t\t\t\ttitle: item.title || '未知优惠券',\n\t\t\t\t\t\t\tstartTime: item.startTime || 0,\n\t\t\t\t\t\t\tday: item.day || 0,\n\t\t\t\t\t\t\trule: item.rule || '无使用规则',\n\t\t\t\t\t\t}));\n\t\t\t\t\t\tif (append) {\n\t\t\t\t\t\t\tthis.couponlist = [...this.couponlist, ...newList];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.couponlist = newList;\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// Update hasMore based on data received\n\t\t\t\t\t\tthis.hasMore =\n\t\t\t\t\t\t\tthis.couponlist.length < this.total &&\n\t\t\t\t\t\t\tnewList.length === this.pageSize;\n\t\t\t\t\t\tif (!this.hasMore && this.couponlist.length > 0) {\n\t\t\t\t\t\t\tthis.loadingText = '没有更多数据了';\n\t\t\t\t\t\t\tthis.loadingStatus = true;\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.loadingStatus = false;\n\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tresolve(res);\n\t\t\t\t\t})\n\t\t\t\t\t.catch((e) => {\n\t\t\t\t\t\tconsole.error('Failed to fetch coupon list:', e);\n\t\t\t\t\t\tif (append && this.page > 1) {\n\t\t\t\t\t\t\tthis.page -= 1; // Roll back page number on failure\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst errorMsg =\n\t\t\t\t\t\t\ttypeof e === 'string' ? e : e.message || '获取优惠券失败';\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: errorMsg,\n\t\t\t\t\t\t});\n\t\t\t\t\t\treject(e);\n\t\t\t\t\t});\n\t\t\t});\n\t\t},\n\t},\n\twatch: {\n\t\tcurrentIndex(newVal) {\n\t\t\tthis.page = 1;\n\t\t\tthis.couponlist = [];\n\t\t\tthis.total = 0;\n\t\t\tthis.hasMore = true;\n\t\t\tthis.loadingStatus = true;\n\t\t\tthis.loadingText = '正在加载...';\n\t\t\tthis.getList().finally(() => {\n\t\t\t\tthis.loadingStatus = false;\n\t\t\t});\n\t\t},\n\t},\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n\theight: 100vh;\n\tbackground-color: #f8f8f8;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.header {\n\tbackground-color: #fff;\n\theight: 100rpx;\n\tdisplay: flex;\n\tjustify-content: space-around;\n\talign-items: center;\n\tborder-top: 1px solid #f6f6f6;\n\n\t.header-item {\n\t\t.tag {\n\t\t\twidth: 38rpx;\n\t\t\theight: 6rpx;\n\t\t\tbackground: #2e80fe;\n\t\t\tborder-radius: 4rpx;\n\t\t\tmargin: auto;\n\t\t\tmargin-top: 10rpx;\n\t\t}\n\t}\n}\n\n.main {\n\tflex: 1;\n\tpadding: 40rpx 30rpx;\n\toverflow-y: auto;\n\n\t.main_item {\n\t\twidth: 690rpx;\n\t\theight: 202rpx;\n\t\tbackground: #ffffff;\n\t\tborder-radius: 20rpx;\n\t\tmargin-bottom: 20rpx;\n\n\t\t.top {\n\t\t\theight: 150rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tpadding-top: 26rpx;\n\t\t\tpadding-left: 24rpx;\n\t\t\tpadding-right: 14rpx;\n\t\t\tposition: relative;\n\t\t\tborder-bottom: 2rpx solid #e9e9e9;\n\n\t\t\t.box1 {\n\t\t\t\ttext-align: center;\n\t\t\t\twidth: 185rpx;\n\t\t\t\tfont-size: 40rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #e72427;\n\n\t\t\t\tspan {\n\t\t\t\t\tfont-size: 15rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.box2 {\n\t\t\t\tmargin-left: 28rpx;\n\n\t\t\t\ttext {\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #171717;\n\t\t\t\t\tmax-width: 450rpx;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t}\n\n\t\t\t\tspan {\n\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\tcolor: #b2b2b2;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.box3 {\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 24rpx;\n\t\t\t\ttop: 24rpx;\n\t\t\t\twidth: 100rpx;\n\t\t\t\theight: 42rpx;\n\t\t\t\tbackground: #2e80fe;\n\t\t\t\tborder-radius: 22rpx;\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #ffffff;\n\t\t\t\tline-height: 42rpx;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t}\n\n\t\t.bottom {\n\t\t\tpadding: 0 24rpx;\n\t\t\tmax-width: 500rpx;\n\t\t\toverflow: hidden;\n\t\t\twhite-space: nowrap;\n\t\t\ttext-overflow: ellipsis;\n\t\t\tline-height: 50rpx;\n\t\t\tfont-size: 20rpx;\n\t\t\tfont-weight: 400;\n\t\t\tcolor: #b2b2b2;\n\t\t}\n\t}\n\n\t.loading-status {\n\t\ttext-align: center;\n\t\tpadding: 40rpx 0;\n\t\tcolor: #999999;\n\t\tfont-size: 28rpx;\n\t}\n\n\t.empty-data {\n\t\ttext-align: center;\n\t\tpadding: 200rpx 0;\n\t\tcolor: #999999;\n\t\tfont-size: 32rpx;\n\t}\n}\n</style>", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupon.vue?vue&type=style&index=0&id=423579cc&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./coupon.vue?vue&type=style&index=0&id=423579cc&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755670631663\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}