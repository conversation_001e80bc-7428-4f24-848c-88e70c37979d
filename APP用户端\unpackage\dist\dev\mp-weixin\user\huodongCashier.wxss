@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-ba180efa {
  background-color: #f8f8f8;
  height: 100vh;
  padding-top: 40rpx;
}
.page .time.data-v-ba180efa {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.page .time text.data-v-ba180efa {
  margin-right: 15rpx;
}
.page .price.data-v-ba180efa {
  display: inline-block;
  width: 750rpx;
  text-align: center;
  margin-top: 20rpx;
  font-size: 80rpx;
  font-weight: 500;
  color: #292C39;
}
.page .price ._span.data-v-ba180efa {
  font-size: 36rpx;
}
.page .payCard.data-v-ba180efa {
  margin: 0 auto;
  width: 686rpx;
  height: 130rpx;
  background: #FFFFFF;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  margin-top: 40rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.page .payCard .left.data-v-ba180efa {
  display: flex;
  align-items: center;
}
.page .payCard .left image.data-v-ba180efa {
  width: 70rpx;
  height: 70rpx;
}
.page .payCard .left text.data-v-ba180efa {
  margin-left: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .payCard .right.data-v-ba180efa {
  display: flex;
  align-items: center;
}
.page .payCard .right text.data-v-ba180efa {
  font-size: 24rpx;
  font-weight: 400;
  color: #E72427;
}
.page .payCard .choose.data-v-ba180efa {
  width: 40rpx;
  height: 40rpx;
  background-color: #fff;
  border: 2rpx solid #ADADAD;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page .footer.data-v-ba180efa {
  width: 750rpx;
  height: 192rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 0;
}
.page .footer .btn.data-v-ba180efa {
  width: 686rpx;
  height: 88rpx;
  background: #2E80FE;
  border-radius: 44rpx 44rpx 44rpx 44rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 88rpx;
  text-align: center;
}

