<wxs src="./scrollWxs.wxs" module="wxs"></wxs>
<view data-ref="u-scroll-list" class="u-scroll-list data-v-e2a26316 vue-ref"><scroll-view class="u-scroll-list__scroll-view data-v-e2a26316" scroll-x="{{true}}" data-scrollWidth="{{scrollWidth}}" data-barWidth="{{$root.g0}}" data-indicatorWidth="{{$root.g1}}" show-scrollbar="{{false}}" upper-threshold="{{0}}" lower-threshold="{{0}}" bindscroll="{{wxs.scroll}}" bindscrolltoupper="{{wxs.scrolltoupper}}" bindscrolltolower="{{wxs.scrolltolower}}"><view class="u-scroll-list__scroll-view__content data-v-e2a26316"><slot></slot></view></scroll-view><block wx:if="{{indicator}}"><view class="u-scroll-list__indicator data-v-e2a26316" style="{{$root.s0}}"><view class="u-scroll-list__indicator__line data-v-e2a26316" style="{{$root.s1}}"><view data-ref="u-scroll-list__indicator__line__bar" class="u-scroll-list__indicator__line__bar data-v-e2a26316 vue-ref" style="{{$root.s2}}"></view></view></view></block></view>