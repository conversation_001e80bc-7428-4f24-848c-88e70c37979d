@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-92a41b30 {
  background: #F8F8F8;
  height: 100vh;
}
.page .box.data-v-92a41b30 {
  padding: 50rpx 82rpx;
  background: #fff;
}
.page .box .money.data-v-92a41b30 {
  margin: 0 auto;
  width: -webkit-fit-content;
  width: fit-content;
  font-size: 80rpx;
  font-weight: 500;
  color: #171717;
}
.page .box .title.data-v-92a41b30 {
  margin: 0 auto;
  margin-top: 20rpx;
  width: -webkit-fit-content;
  width: fit-content;
  font-size: 24rpx;
  font-weight: 400;
  color: #171717;
}
.page .box .btn.data-v-92a41b30 {
  margin: 0 auto;
  margin-top: 64rpx;
  width: 584rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  line-height: 98rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
}

