@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-4df5043e {
  height: 100vh;
  padding: 40rpx 0;
  overflow: auto;
  padding-bottom: 200rpx;
  background-color: #f8f8f8;
}
.page .address_item.data-v-4df5043e {
  background-color: #fff;
  padding: 18rpx 30rpx;
  margin-bottom: 20rpx;
}
.page .address_item .head.data-v-4df5043e {
  display: flex;
  align-items: center;
}
.page .address_item .head .mr.data-v-4df5043e {
  width: 72rpx;
  height: 38rpx;
  background: #CCE0FF;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  border: 2rpx solid #2E80FE;
  font-size: 20rpx;
  font-weight: 400;
  color: #2E80FE;
  line-height: 38rpx;
  text-align: center;
}
.page .address_item .head text.data-v-4df5043e {
  margin-left: 36rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}
.page .address_item .head ._span.data-v-4df5043e {
  margin-left: 20rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
}
.page .address_item .body.data-v-4df5043e {
  margin-top: 18rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page .address_item .body .left.data-v-4df5043e {
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .address_item .body .right image.data-v-4df5043e {
  width: 32rpx;
  height: 32rpx;
}
.page .address_item .foot.data-v-4df5043e {
  margin-top: 20rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
}
.page .address_item .foot .box.data-v-4df5043e {
  margin-right: 15rpx;
}
.page .load-more.data-v-4df5043e {
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: #2e80fe;
  cursor: pointer;
}
.page .footer.data-v-4df5043e {
  padding: 52rpx 30rpx;
  position: fixed;
  bottom: 0;
  background-color: #fff;
}
.page .footer .btn.data-v-4df5043e {
  width: 690rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 98rpx;
  text-align: center;
}

