@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-d1b2191e {
  background-color: #f8f8f8;
  min-height: 100vh;
  padding: 20rpx 0;
}
.page .header.data-v-d1b2191e {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  font-weight: 500;
  color: #3b3b3b;
  padding: 0 30rpx;
  width: 750rpx;
  height: 118rpx;
  background: #ffffff;
}
.page .header .right.data-v-d1b2191e {
  display: flex;
  align-items: center;
}
.page .mid.data-v-d1b2191e {
  margin-top: 20rpx;
  width: 750rpx;
  height: 276rpx;
  background: #ffffff;
  padding: 0 30rpx;
  padding-top: 40rpx;
}
.page .mid .title.data-v-d1b2191e {
  font-size: 28rpx;
  font-weight: 500;
  color: #3b3b3b;
}
.page .mid .top.data-v-d1b2191e {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding-top: 28rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f2f3f6;
}
.page .mid .top .r_left.data-v-d1b2191e {
  font-size: 28rpx;
  font-weight: 500;
  color: #e51837;
}
.page .mid .bottom.data-v-d1b2191e {
  padding-top: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
}
.page .btn.data-v-d1b2191e {
  margin: 60rpx auto 0;
  width: 690rpx;
  height: 98rpx;
  background: #2e80fe;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  line-height: 98rpx;
  text-align: center;
}
.page .btn.data-v-d1b2191e:disabled {
  background: #cccccc;
}
.page .tips.data-v-d1b2191e {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
  text-align: center;
  margin-top: 20rpx;
}
.page .contact.data-v-d1b2191e {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #999999;
  text-align: center;
  margin-top: 20rpx;
}
.page .contact .phone.data-v-d1b2191e {
  color: #2e80fe;
  text-decoration: underline;
}

