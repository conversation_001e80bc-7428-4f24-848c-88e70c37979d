@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
view.data-v-4661e472, scroll-view.data-v-4661e472, swiper-item.data-v-4661e472 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-notice.data-v-4661e472 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.u-notice__left-icon.data-v-4661e472 {
  align-items: center;
  margin-right: 5px;
}
.u-notice__right-icon.data-v-4661e472 {
  margin-left: 5px;
  align-items: center;
}
.u-notice__content.data-v-4661e472 {
  text-align: right;
  flex: 1;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow: hidden;
}
.u-notice__content__text.data-v-4661e472 {
  font-size: 14px;
  color: #f9ae3d;
  padding-left: 100%;
  word-break: keep-all;
  white-space: nowrap;
  -webkit-animation: u-loop-animation-data-v-4661e472 10s linear infinite both;
          animation: u-loop-animation-data-v-4661e472 10s linear infinite both;
  display: flex;
  flex-direction: row;
}
@-webkit-keyframes u-loop-animation-data-v-4661e472 {
0% {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
}
100% {
    -webkit-transform: translate3d(-100%, 0, 0);
            transform: translate3d(-100%, 0, 0);
}
}
@keyframes u-loop-animation-data-v-4661e472 {
0% {
    -webkit-transform: translate3d(0, 0, 0);
            transform: translate3d(0, 0, 0);
}
100% {
    -webkit-transform: translate3d(-100%, 0, 0);
            transform: translate3d(-100%, 0, 0);
}
}

