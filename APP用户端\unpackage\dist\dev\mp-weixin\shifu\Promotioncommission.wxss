@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-6f478b84 {
  padding: 44rpx 30rpx;
}
.page .list_item.data-v-6f478b84 {
  height: 102rpx;
  display: flex;
}
.page .list_item .left.data-v-6f478b84 {
  width: 78rpx;
  height: 78rpx;
  border-radius: 50%;
  background: #F9F9F9;
  display: flex;
  justify-content: center;
  align-items: center;
}
.page .list_item .left image.data-v-6f478b84 {
  width: 33rpx;
  height: 31rpx;
}
.page .list_item .mid.data-v-6f478b84 {
  margin-left: 20rpx;
  width: 520rpx;
}
.page .list_item .mid .name1.data-v-6f478b84 {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .list_item .mid .time.data-v-6f478b84 {
  margin-top: 12rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .list_item .right.data-v-6f478b84 {
  width: 92rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
}

