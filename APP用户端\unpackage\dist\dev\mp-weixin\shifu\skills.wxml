<view class="page data-v-2b3aa6dc"><view class="main data-v-2b3aa6dc"><view class="left data-v-2b3aa6dc"><scroll-view class="scrollL data-v-2b3aa6dc" scroll-y="true"><block wx:if="{{loading}}"><view class="loading data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">加载中...</text></view></block><block wx:else><block wx:if="{{error}}"><view class="error data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">{{error}}</text></view></block><block wx:else><block wx:if="{{!$root.g0}}"><view class="no-content data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">暂无分类数据</text></view></block><block wx:else><block wx:for="{{categories}}" wx:for-item="category" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['selectCategory',['$0'],[[['categories','id',category.id,'id']]]]]]]}}" class="{{['left_item','data-v-2b3aa6dc',(selectedCategoryId===category.id)?'active':'']}}" bindtap="__e"><view class="category_name data-v-2b3aa6dc">{{category.name}}</view></view></block></block></block></block></scroll-view></view><view class="right data-v-2b3aa6dc"><scroll-view class="scrollR data-v-2b3aa6dc" scroll-y="true"><block wx:if="{{loading}}"><view class="loading data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">加载中...</text></view></block><block wx:else><block wx:if="{{error}}"><view class="error data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">{{error}}</text></view></block><block wx:else><block wx:if="{{$root.g1}}"><view class="data-v-2b3aa6dc"><block wx:for="{{$root.l1}}" wx:for-item="subCategory" wx:for-index="__i1__" wx:key="id"><view class="subcategory_section data-v-2b3aa6dc"><view class="subcategory_header data-v-2b3aa6dc"><view data-event-opts="{{[['tap',[['toggleSubCategory',['$0'],[[['currentCategory.children','id',subCategory.$orig.id,'id']]]]]]]}}" class="subcategory_title data-v-2b3aa6dc" bindtap="__e">{{''+subCategory.$orig.name+''}}<text class="selected_count data-v-2b3aa6dc">{{"(已选择"+subCategory.m0+")"}}</text></view><view data-event-opts="{{[['tap',[['selectAllServices',['$0'],[[['currentCategory.children','id',subCategory.$orig.id,'id']]]]]]]}}" class="select_all data-v-2b3aa6dc" bindtap="__e">{{''+(subCategory.m1?'取消全选':'全选')+''}}</view><view data-event-opts="{{[['tap',[['toggleSubCategory',['$0'],[[['currentCategory.children','id',subCategory.$orig.id,'id']]]]]]]}}" class="expand_icon data-v-2b3aa6dc" bindtap="__e">{{''+(subCategory.g2?'▲':'▼')+''}}</view></view><block wx:if="{{subCategory.g3}}"><view class="service_items data-v-2b3aa6dc"><block wx:for="{{subCategory.l0}}" wx:for-item="service" wx:for-index="__i2__" wx:key="id"><view data-event-opts="{{[['tap',[['toggleSelectService',['$0','$1'],[[['currentCategory.children','id',subCategory.$orig.id],['serviceList','id',service.$orig.id,'id']],[['currentCategory.children','id',subCategory.$orig.id,'id']]]]]]]}}" class="{{['service_item','data-v-2b3aa6dc',(service.m2)?'active':'']}}" bindtap="__e">{{''+service.$orig.title+''}}</view></block></view></block><block wx:else><block wx:if="{{subCategory.g4}}"><view class="no-services data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">暂无服务项目</text></view></block></block></view></block></view></block><block wx:else><view class="no-content data-v-2b3aa6dc"><text class="data-v-2b3aa6dc">暂无子分类</text></view></block></block></block></scroll-view></view></view><view class="footer data-v-2b3aa6dc"><button data-event-opts="{{[['tap',[['saveSettings',['$event']]]]]}}" class="save_btn data-v-2b3aa6dc" bindtap="__e">保存设置</button></view></view>