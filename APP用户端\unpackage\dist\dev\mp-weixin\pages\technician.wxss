
.page.data-v-5d289ec6 {
	height: 100vh;
	overflow: hidden;
	background-color: #f8f8f8;
	/* 确保 tabbar 不遮挡内容 */
	box-sizing: border-box;
	padding-bottom: 80rpx;
}
.header.data-v-5d289ec6 {
	padding: 0 15rpx;
	display: flex;
	align-items: center;
	background-color: #fff;
	height: 10vh;
	position: relative;
	z-index: 10;
}
.main.data-v-5d289ec6 {
	display: flex;
	/* 计算剩余高度 */
	height: calc(90vh - 80rpx);
}
.left.data-v-5d289ec6 {
	width: 190rpx;
	background-color: #f8f8f8;
	/* 确保 left 容器高度和 main 一致 */
	height: 100%;
}
.scrollL.data-v-5d289ec6 {
	/* 修复：设置准确的滚动容器高度 */
	height: 100%;
	overflow-y: auto;
	/* 确保内容可以完整滚动 */
	padding-bottom: 20rpx;
}
.left_item.data-v-5d289ec6 {
	padding: 0;
	min-height: 100rpx;
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	transition: all 0.2s;
	display: flex;
	flex-direction: column;
}
.category_name.data-v-5d289ec6 {
	height: 100rpx;
	width: 100%;
	display: flex;
	align-items: center;
	padding: 0 20rpx;
	border-left: 6rpx solid transparent;
	transition: all 0.2s;
	/* 防止文本换行 */
	white-space: nowrap;
	box-sizing: border-box;
}
.category_name.active.data-v-5d289ec6 {
	color: #fff;
	background-color: #2e80fe;
	border-left-color: #2e80fe;
}
.sub_categories.data-v-5d289ec6 {
	width: 100%;
	padding-left: 35rpx;
	font-size: 20rpx;
	display: flex;
	flex-direction: column;
	background-color: #fff;
}
.sub_category.data-v-5d289ec6 {
	min-height: 80rpx;
	line-height: 80rpx;
	font-size: 26rpx;
	font-weight: 400;
	color: #666;
	transition: color 0.2s;
	position: relative;
}
.sub_category.active.data-v-5d289ec6 {
	color: #2e80fe;
	font-weight: 500;
}
.sub_category.active.data-v-5d289ec6::before {
	content: '';
	position: absolute;
	left: -15rpx;
	top: 50%;
	-webkit-transform: translateY(-50%);
	        transform: translateY(-50%);
	width: 6rpx;
	height: 30rpx;
	background-color: #2e80fe;
	border-radius: 3rpx;
}

/* 新增：底部占位空间样式 */
.bottom_placeholder.data-v-5d289ec6 {
	height: 40rpx;
	width: 100%;
}
.right.data-v-5d289ec6 {
	flex: 1;
	background-color: #fff;
	border-radius: 12rpx 12rpx 0 0;
	margin-left: 10rpx;
	/* 确保 right 容器高度和 main 一致 */
	height: 100%;
	overflow: hidden; /* 防止内部滚动条影响布局 */
}
.scrollR.data-v-5d289ec6 {
	height: 100%;
	overflow-y: auto;
}
.right_box.data-v-5d289ec6 {
	padding: 24rpx 9rpx;
}
.right_box .title.data-v-5d289ec6 {
	padding-left: 15rpx;
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 20rpx;
}
.img.data-v-5d289ec6 {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
}
.img_item.data-v-5d289ec6 {
	width: 220rpx;
	margin: 10rpx 15rpx;
	background-color: #fff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	transition: -webkit-transform 0.2s;
	transition: transform 0.2s;
	transition: transform 0.2s, -webkit-transform 0.2s;
	box-sizing: border-box;
}
.img_item.data-v-5d289ec6:hover {
	-webkit-transform: translateY(-4rpx);
	        transform: translateY(-4rpx);
}
.img_item image.data-v-5d289ec6 {
	width: 144rpx;
	height: 144rpx;
	border-radius: 12rpx;
	margin-bottom: 12rpx;
}
.lname.data-v-5d289ec6 {
	font-size: 24rpx;
	font-weight: 400;
	color: #333;
	text-align: center;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	width: 100%;
}
.no-content.data-v-5d289ec6,
.loading.data-v-5d289ec6,
.error.data-v-5d289ec6 {
	text-align: center;
	color: #999;
	font-size: 28rpx;
	padding: 40rpx;
}
.error.data-v-5d289ec6 {
	color: #ff4d4f;
}

