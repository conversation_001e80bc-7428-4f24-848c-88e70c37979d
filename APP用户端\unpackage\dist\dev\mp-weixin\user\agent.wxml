<view class="page data-v-06dcff4f"><block wx:if="{{$root.g0}}"><view class="data-v-06dcff4f"><block wx:for="{{serviceDet}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="site_item data-v-06dcff4f"><view class="image-container data-v-06dcff4f"><image src="{{item.img||'/static/images/placeholder.png'}}" mode="scaleToFill" class="data-v-06dcff4f"></image></view><view class="content data-v-06dcff4f"><view class="name data-v-06dcff4f">{{item.name}}</view><view class="address data-v-06dcff4f"><view data-event-opts="{{[['tap',[['copyAddress',['$0'],[[['serviceDet','',index,'address']]]]]]]}}" class="position data-v-06dcff4f" bindtap="__e"><text class="data-v-06dcff4f">{{item.address}}</text><u-icon vue-id="{{'567928d2-1-'+index}}" name="arrow-right" color="#333" size="10" class="data-v-06dcff4f" bind:__l="__l"></u-icon></view><view style="display:flex;flex-direction:column;align-items:flex-end;gap:16rpx;" class="data-v-06dcff4f"><u-icon vue-id="{{'567928d2-2-'+index}}" name="attach" size="28" data-event-opts="{{[['^tap',[['seeImg',['$0'],[[['serviceDet','',index,'sunCode']]]]]]]}}" bind:tap="__e" class="data-v-06dcff4f" bind:__l="__l"></u-icon><u-icon vue-id="{{'567928d2-3-'+index}}" name="phone-fill" color="#599eff" size="28" data-event-opts="{{[['^tap',[['phoneDLD',['$0'],[[['serviceDet','',index,'tel']]]]]]]}}" bind:tap="__e" class="data-v-06dcff4f" bind:__l="__l"></u-icon></view></view></view></view></block></view></block></view>