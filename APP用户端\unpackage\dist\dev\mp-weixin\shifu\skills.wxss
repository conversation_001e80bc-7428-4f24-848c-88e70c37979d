
.page.data-v-2b3aa6dc {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为固定footer预留空间 */
}
.main.data-v-2b3aa6dc {
  flex: 1;
  display: flex;
  overflow: hidden;
}
.left.data-v-2b3aa6dc {
  width: 190rpx;
  background-color: #f8f8f8;
}
.scrollL.data-v-2b3aa6dc {
  height: 100%;
  overflow-y: auto;
}
.left_item.data-v-2b3aa6dc {
  padding: 0 20rpx;
  min-height: 100rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  border-left: 6rpx solid transparent;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
}
.left_item.active.data-v-2b3aa6dc {
  color: #2e80fe;
  font-size: 30rpx;
  border-left-color: #2e80fe;
  background-color: #fff;
}
.category_name.data-v-2b3aa6dc {
  height: 100rpx;
  width: 100%;
  display: flex;
  align-items: center;
}
.right.data-v-2b3aa6dc {
  flex: 1;
  background-color: #fff;
  border-radius: 12rpx 12rpx 0 0;
  margin-left: 10rpx;
}
.scrollR.data-v-2b3aa6dc {
  height: 100%;
  overflow-y: auto;
}
.subcategory_section.data-v-2b3aa6dc {
  margin-bottom: 15rpx;
}
.subcategory_header.data-v-2b3aa6dc {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fafafa;
  border-bottom: 1rpx solid #f0f0f0;
}
.subcategory_title.data-v-2b3aa6dc {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}
.selected_count.data-v-2b3aa6dc {
  color: #2e80fe;
  font-weight: normal;
}
.select_all.data-v-2b3aa6dc {
  font-size: 26rpx;
  color: #2e80fe;
  margin-left: 20rpx;
}
.expand_icon.data-v-2b3aa6dc {
  font-size: 24rpx;
  color: #999;
}
.service_items.data-v-2b3aa6dc {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}
.service_item.data-v-2b3aa6dc {
  width: calc(33.33% - 20rpx);
  margin: 10rpx;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s;
  text-align: center;
  padding: 0 10rpx;
}
.service_item.active.data-v-2b3aa6dc {
  background-color: #e6f0ff;
  color: #2e80fe;
  border: 1rpx solid #2e80fe;
}
.no-services.data-v-2b3aa6dc,
.no-content.data-v-2b3aa6dc,
.loading.data-v-2b3aa6dc,
.error.data-v-2b3aa6dc {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx;
}
.error.data-v-2b3aa6dc {
  color: #ff4d4f;
}
.footer.data-v-2b3aa6dc {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  padding: 15rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}
.save_btn.data-v-2b3aa6dc {
  width: 90%;
  height: 90rpx;
  background-color: #2e80fe;
  color: white;
  border-radius: 45rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

