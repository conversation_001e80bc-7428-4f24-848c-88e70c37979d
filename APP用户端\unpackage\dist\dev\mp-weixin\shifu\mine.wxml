<view class="pages-mine"><block wx:if="{{!isLoading}}"><view class="header"><view class="header-content"><view class="avatar_view"><image class="avatar" mode="aspectFill" src="{{userInfo.avatarUrl}}"></image></view><view class="user-info"><view class="user-info-logged"><view class="nickname">{{''+userInfo.nickName+''}}</view><block wx:if="{{userInfo.phone}}"><view class="phone-number">{{''+userInfo.phone+''}}</view></block><block wx:if="{{userInfo.status!==undefined}}"><view class="{{['status-badge',statusBadgeClass]}}">{{''+statusText+''}}</view></block></view></view><view data-event-opts="{{[['tap',[['navigateTo',['../shifu/userProfile']]]]]}}" class="settings" bindtap="__e"><view class="iconfont icon-xitong text-bold _i"></view></view></view></view></block><view class="mine-menu-list box-shadow fill-base box1"><view class="menu-title flex-between pl-lg pr-md b-1px-b"><view class="f-paragraph c-title text-bold">我的订单</view></view><view data-event-opts="{{[['tap',[['dingyue']]]]}}" class="flex-warp pt-lg pb-lg" bindtap="__e"><block wx:for="{{orderList2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleNavigate',['$0'],[[['orderList2','',index,'url']]]]]]]}}" class="order-item" bindtap="__e"><view class="icon-container"><u-icon vue-id="{{'6c16a88e-1-'+index}}" name="{{item.icon}}" color="#448cfb" size="28" bind:__l="__l"></u-icon><block wx:if="{{item.count>0}}"><view class="number-circle">{{item.count}}</view></block></view><view class="mt-sm">{{item.text}}</view></view></block></view></view><view data-event-opts="{{[['tap',[['dingyue']]]]}}" class="mine-menu-list box-shadow fill-base" bindtap="__e"><view class="menu-title flex-between pl-lg pr-md b-1px-b"><view class="f-paragraph c-title text-bold">常用功能</view></view><view class="flex-warp pt-lg pb-lg"><block wx:for="{{orderList3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleNavigate',['$0'],[[['orderList3','',index,'url']]]]]]]}}" class="order-item" bindtap="__e"><u-icon vue-id="{{'6c16a88e-2-'+index}}" name="{{item.icon}}" color="#448cfb" size="28" bind:__l="__l"></u-icon><view class="mt-sm">{{item.text}}</view></view></block></view></view><view class="mine-menu-list box-shadow fill-base"><view class="menu-title flex-between pl-lg pr-md b-1px-b"><view class="f-paragraph c-title text-bold">其他功能</view></view><view class="flex-warp pt-lg pb-lg"><view data-event-opts="{{[['tap',[['handleNavigate',['/shifu/skills']]]]]}}" class="order-item" bindtap="__e"><u-icon vue-id="6c16a88e-3" name="plus-square-fill" color="#448cfb" size="28" bind:__l="__l"></u-icon><view class="mt-sm">技能标签</view></view><view data-event-opts="{{[['tap',[['handleNavigate',['/shifu/Professiona']]]]]}}" class="order-item" bindtap="__e"><u-icon vue-id="6c16a88e-4" name="order" color="#448cfb" size="28" bind:__l="__l"></u-icon><view class="mt-sm">技能证书</view></view><view data-event-opts="{{[['tap',[['handleNavigate',['/user/promotion']]]]]}}" class="order-item" bindtap="__e"><u-icon vue-id="6c16a88e-5" name="red-packet-fill" color="#E41F19" size="28" bind:__l="__l"></u-icon><view class="mt-sm" style="color:#E41F19;">邀请有礼</view></view></view></view><view class="spacer"></view><view class="mine-tool-grid fill-base"><view class="grid-container"><block wx:for="{{toolList2}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleNavigate',['$0'],[[['toolList2','',index,'url']]]]]]]}}" class="grid-item" bindtap="__e"><view class="grid-icon-container"><u-icon vue-id="{{'6c16a88e-6-'+index}}" name="{{item.icon}}" color="{{item.iconColor}}" size="28" bind:__l="__l"></u-icon></view><view class="grid-text">{{item.text}}</view></view></block><view data-event-opts="{{[['tap',[['navigateTo',['../pages/service']]]]]}}" class="grid-item" bindtap="__e"><view class="grid-icon-container switch-identity"><u-icon vue-id="6c16a88e-7" name="man-add" color="#E41F19" size="28" bind:__l="__l"></u-icon></view><view class="grid-text" style="color:#E41F19;">切换用户版</view></view><view class="grid-item"><button class="contact-btn-wrapper" open-type="contact" bindcontact="handleContact" session-from="sessionFrom"><view class="grid-icon-container switch-identity"><u-icon vue-id="6c16a88e-8" name="server-man" color="#448cfb" size="28" bind:__l="__l"></u-icon></view><view class="grid-text">客服</view></button></view></view></view><tabbar vue-id="6c16a88e-9" cur="1" bind:__l="__l"></tabbar></view>