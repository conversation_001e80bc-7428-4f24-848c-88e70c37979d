@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-105ecd58 {
  background: #f3f4f5;
  height: 100vh;
}
.page .header.data-v-105ecd58 {
  padding: 40rpx 32rpx;
}
.page .header .title.data-v-105ecd58 {
  font-size: 40rpx;
  font-weight: 500;
  color: #333333;
}
.page .header .desc.data-v-105ecd58 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
}
.page .header .time.data-v-105ecd58 {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
}
.page .header .time.data-v-105ecd58  .u-count-down__text {
  color: #E72427;
}
.page .main.data-v-105ecd58 {
  width: 750rpx;
  height: 82vh;
  padding: 0 32rpx;
  background: #ffffff;
  border-radius: 40rpx 40rpx 0rpx 0rpx;
  padding-bottom: 192rpx;
}
.page .main scroll-view.data-v-105ecd58 {
  height: 100%;
}
.page .main scroll-view .main_item.data-v-105ecd58 {
  padding: 40rpx 0;
  border-bottom: 2rpx solid #F2F3F6;
}
.page .main scroll-view .main_item .time.data-v-105ecd58 {
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
}
.page .main scroll-view .main_item .box.data-v-105ecd58 {
  margin-top: 20rpx;
  display: flex;
}
.page .main scroll-view .main_item .box image.data-v-105ecd58 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}
.page .main scroll-view .main_item .box .mid.data-v-105ecd58 {
  margin-left: 20rpx;
}
.page .main scroll-view .main_item .box .mid .top.data-v-105ecd58 {
  padding-top: 4rpx;
  display: flex;
  align-items: center;
}
.page .main scroll-view .main_item .box .mid .top .name.data-v-105ecd58 {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
.page .main scroll-view .main_item .box .mid .top .level.data-v-105ecd58,
.page .main scroll-view .main_item .box .mid .top .promise.data-v-105ecd58 {
  margin-left: 8rpx;
  width: -webkit-fit-content;
  width: fit-content;
  height: 28rpx;
  background: #2E80FE;
  border-radius: 14rpx 14rpx 14rpx 14rpx;
  padding: 0 14rpx;
  line-height: 28rpx;
  text-align: center;
  font-size: 16rpx;
  font-weight: 400;
  color: #FFFFFF;
}
.page .main scroll-view .main_item .box .mid .bottom.data-v-105ecd58 {
  margin-top: 20rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
}
.page .main scroll-view .main_item .box .mid .bottom ._span.data-v-105ecd58 {
  color: #333333;
}
.page .main scroll-view .main_item .box .price.data-v-105ecd58 {
  flex: 1;
  font-size: 40rpx;
  font-weight: 600;
  color: #E72427;
  text-align: right;
}
.page .main scroll-view .main_item .down.data-v-105ecd58 {
  margin-top: 32rpx;
  display: flex;
  flex-direction: row-reverse;
}
.page .main scroll-view .main_item .down .btn.data-v-105ecd58 {
  width: 240rpx;
  height: 80rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 80rpx;
  text-align: center;
}
.page .footer.data-v-105ecd58 {
  width: 750rpx;
  height: 192rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  bottom: 0;
  background-color: #FFFFFF;
}
.page .footer .btn.data-v-105ecd58 {
  width: 686rpx;
  height: 88rpx;
  background: #2E80FE;
  border-radius: 44rpx 44rpx 44rpx 44rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #FFFFFF;
  line-height: 88rpx;
  text-align: center;
}

