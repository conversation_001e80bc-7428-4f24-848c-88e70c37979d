<view class="page data-v-61a23cfb"><view class="item data-v-61a23cfb"><view class="left data-v-61a23cfb">姓名</view><view class="right data-v-61a23cfb"><input type="text" placeholder="请输入持卡人姓名" data-event-opts="{{[['input',[['__set_model',['$0','name','$event',[]],['form']]]]]}}" value="{{form.name}}" bindinput="__e" class="data-v-61a23cfb"/></view></view><view class="item data-v-61a23cfb"><view class="left data-v-61a23cfb">身份证</view><view class="right data-v-61a23cfb"><input type="text" placeholder="请输入身份证号码" data-event-opts="{{[['input',[['__set_model',['$0','idCard','$event',[]],['form']]]]]}}" value="{{form.idCard}}" bindinput="__e" class="data-v-61a23cfb"/></view></view><view class="item data-v-61a23cfb"><view class="left data-v-61a23cfb">银行卡号</view><view class="right data-v-61a23cfb"><input type="text" placeholder="请输入银行卡号" data-event-opts="{{[['input',[['__set_model',['$0','cardNo','$event',[]],['form']]]]]}}" value="{{form.cardNo}}" bindinput="__e" class="data-v-61a23cfb"/></view></view><view class="item data-v-61a23cfb"><view class="left data-v-61a23cfb">开户银行</view><view class="right data-v-61a23cfb"><input type="text" placeholder="请输入开户银行" data-event-opts="{{[['input',[['__set_model',['$0','bankName','$event',[]],['form']]]]]}}" value="{{form.bankName}}" bindinput="__e" class="data-v-61a23cfb"/></view></view><view class="item data-v-61a23cfb"><view class="left data-v-61a23cfb">银行预留手机号</view><view class="right data-v-61a23cfb"><input type="text" placeholder="请输入手机号码" data-event-opts="{{[['input',[['__set_model',['$0','bankMobile','$event',[]],['form']]]]]}}" value="{{form.bankMobile}}" bindinput="__e" class="data-v-61a23cfb"/></view></view><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="btn data-v-61a23cfb" bindtap="__e">提交信息</view></view>