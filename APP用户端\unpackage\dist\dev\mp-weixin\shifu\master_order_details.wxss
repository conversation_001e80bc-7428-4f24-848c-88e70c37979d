@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-53648d4e {
  min-height: 100vh;
  background: linear-gradient(180deg, #f5f7fa 0%, #e4e7ed 100%);
  padding: 32rpx 24rpx 120rpx 24rpx;
  /* 底部增加padding为报价按钮留空间 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}
.box.data-v-53648d4e {
  margin: 0 auto;
  max-width: 690rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  padding: 32rpx;
}
/* Title Styling */
.title.data-v-53648d4e {
  display: flex;
  align-items: center;
  font-size: 36rpx;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 24rpx;
}
.title ._span.data-v-53648d4e {
  color: #1a1a1a;
  /* For required fields */
}
.title .title-icon.data-v-53648d4e {
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
}
/* Info Box Styling */
.info-box.data-v-53648d4e {
  background: #fafafa;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 32rpx;
}
.info-item.data-v-53648d4e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #e5e7eb;
}
.info-item.data-v-53648d4e:last-child {
  border-bottom: none;
}
.info-item .label.data-v-53648d4e {
  font-size: 28rpx;
  font-weight: 500;
  color: #6b7280;
}
.info-item .value.data-v-53648d4e {
  font-size: 30rpx;
  font-weight: 600;
  color: #1a1a1a;
  max-width: 420rpx;
  white-space: normal;
  word-break: break-all;
  line-height: 1.4;
}
.info-item .value.align-right.data-v-53648d4e {
  text-align: right;
}
.navigation-btn.data-v-53648d4e {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 180rpx;
  height: 64rpx;
  background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
  border-radius: 32rpx;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 24rpx;
  margin-left: auto;
}
.navigation-btn .nav-icon.data-v-53648d4e {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}
/* Dynamic Section Styling */
.dynamic-section.data-v-53648d4e {
  margin-bottom: 32rpx;
}
.img-box.data-v-53648d4e {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 24rpx;
}
.dynamic-image.data-v-53648d4e {
  width: 196rpx;
  height: 196rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e5e7eb;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
/* Image Modal Styling */
.image-modal.data-v-53648d4e {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content.data-v-53648d4e {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 90%;
  max-height: 90%;
}
.modal-image.data-v-53648d4e {
  max-width: 100%;
  max-height: 80vh;
  border-radius: 16rpx;
}
.close-btn.data-v-53648d4e {
  margin-top: 20rpx;
  padding: 16rpx 32rpx;
  background: #ffffff;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 500;
}
/* Text and Notes Styling */
.text-box.data-v-53648d4e {
  margin-top: 24rpx;
  background: #f3f4f6;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  color: #4b5563;
}
.notes-box.data-v-53648d4e {
  margin-top: 24rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  font-size: 28rpx;
  color: #374151;
  line-height: 1.5;
  word-break: break-all;
}
/* Bottom quote area style */
.bottom-quote-section.data-v-53648d4e {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top: 1rpx solid #e5e7eb;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  z-index: 100;
}
.quote-btn.data-v-53648d4e {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(90deg, #2E80FE 0%, #5BA0FF 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(46, 128, 254, 0.3);
}
/* Quote popup style */
.quote-popup-box.data-v-53648d4e {
  padding: 40rpx 30rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  min-height: 400rpx;
}
.quote-popup-box .popup-title.data-v-53648d4e {
  text-align: center;
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 40rpx;
}
.quote-popup-box .popup-subtitle.data-v-53648d4e {
  font-size: 28rpx;
  font-weight: 500;
  color: #374151;
  margin-bottom: 20rpx;
}
.quote-popup-box .money-input.data-v-53648d4e {
  margin-bottom: 40rpx;
}
.quote-popup-box .confirm-quote-btn.data-v-53648d4e {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(90deg, #2E80FE 0%, #5BA0FF 100%);
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(46, 128, 254, 0.3);
}
.image-modal.data-v-53648d4e {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
.modal-content.data-v-53648d4e {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100vw;
  height: 100vh;
  position: relative;
}
.modal-image.data-v-53648d4e {
  width: 100vw;
  height: 100vh;
  object-fit: contain;
  /* Ensures image scales to fit without distortion */
  border-radius: 0;
  /* Remove border-radius for full-screen effect */
}
.close-btn.data-v-53648d4e {
  position: absolute;
  bottom: 20rpx;
  padding: 16rpx 32rpx;
  background: #ffffff;
  border-radius: 32rpx;
  font-size: 28rpx;
  color: #1a1a1a;
  font-weight: 500;
  z-index: 1100;
  /* Ensure button is above image */
}
/* Optimized styles for the name/ID modal content */
.slot-content.data-v-53648d4e {
  padding: 20rpx 0;
}
.main_item.data-v-53648d4e {
  margin-bottom: 32rpx;
  padding: 0 24rpx;
}
.main_item .title.data-v-53648d4e {
  margin-bottom: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #1a1a1a;
  display: flex;
  align-items: center;
}
.main_item .title ._span.data-v-53648d4e {
  color: #1A1A1A;
  margin-right: 8rpx;
}
.main_item .modal-input.data-v-53648d4e {
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border: 1rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 80rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
  transition: all 0.2s ease-in-out;
}
.main_item .modal-input.data-v-53648d4e:focus {
  border-color: #2e80fe;
  background: #ffffff;
  box-shadow: 0 0 8rpx rgba(46, 128, 254, 0.2);
}
.main_item .modal-input.data-v-53648d4e:disabled {
  background: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}
.main_item .card.data-v-53648d4e {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}
.main_item .card .card_item.data-v-53648d4e {
  width: 48%;
  background: #f2fafe;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  transition: -webkit-transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out;
  transition: transform 0.2s ease-in-out, -webkit-transform 0.2s ease-in-out;
}
.main_item .card .card_item.data-v-53648d4e:hover {
  -webkit-transform: translateY(-4rpx);
          transform: translateY(-4rpx);
}
.main_item .card .card_item .top.data-v-53648d4e {
  height: 180rpx;
  width: 100%;
  padding-top: 20rpx;
}
.main_item .card .card_item .top .das.data-v-53648d4e {
  margin: 0 auto;
  width: 85%;
  height: 120rpx;
  border: 2rpx dashed #2e80fe;
  border-radius: 8rpx;
  padding: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.main_item .card .card_item .top .das .up.data-v-53648d4e {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.main_item .card .card_item .bottom.data-v-53648d4e {
  height: 60rpx;
  width: 100%;
  background: linear-gradient(90deg, #2e80fe 0%, #5ba0ff 100%);
  font-size: 24rpx;
  font-weight: 500;
  color: #ffffff;
  text-align: center;
  line-height: 60rpx;
}

