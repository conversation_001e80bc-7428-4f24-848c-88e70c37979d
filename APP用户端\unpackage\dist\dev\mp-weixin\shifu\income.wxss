@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-e4f6867a {
  height: 100vh;
  background-color: #fff;
  padding: 40rpx 30rpx;
}
.page .box.data-v-e4f6867a {
  width: 690rpx;
  height: 316rpx;
  position: relative;
  background: #0D88F9;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  color: #fff;
  padding: 98rpx 40rpx;
  overflow: hidden;
}
.page .box .title.data-v-e4f6867a {
  font-size: 24rpx;
  font-weight: 500;
}
.page .box .money.data-v-e4f6867a {
  font-size: 48rpx;
  font-weight: 500;
  margin-top: 20rpx;
}
.page .box .btn.data-v-e4f6867a {
  width: 132rpx;
  height: 66rpx;
  background: #FABC3F;
  border-radius: 34rpx 34rpx 34rpx 34rpx;
  line-height: 66rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 500;
  position: absolute;
  right: 38rpx;
  top: 72rpx;
  z-index: 2;
}
.page .box .btn-disabled.data-v-e4f6867a {
  background: #cccccc !important;
  color: #666666 !important;
  pointer-events: none;
}
.page .box .circle.data-v-e4f6867a {
  width: 350rpx;
  height: 350rpx;
  border-radius: 50%;
  background-color: #fff;
  color: #fff;
  opacity: 0.2;
  position: absolute;
  right: -100rpx;
  bottom: -120rpx;
  z-index: 1;
}
.page .name.data-v-e4f6867a {
  margin: 40rpx 0;
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .record.data-v-e4f6867a {
  width: 100%;
}
.page .record .list_item.data-v-e4f6867a {
  height: 102rpx;
  display: flex;
}
.page .record .list_item .left.data-v-e4f6867a {
  width: 78rpx;
  height: 78rpx;
  border-radius: 50%;
  background: #F9F9F9;
  display: flex;
  justify-content: center;
  align-items: center;
}
.page .record .list_item .left image.data-v-e4f6867a {
  width: 33rpx;
  height: 31rpx;
}
.page .record .list_item .mid.data-v-e4f6867a {
  margin-left: 20rpx;
  width: 520rpx;
}
.page .record .list_item .mid .name1.data-v-e4f6867a {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
  max-width: 500rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .record .list_item .mid .time.data-v-e4f6867a {
  margin-top: 12rpx;
  font-size: 24rpx;
  font-weight: 400;
  color: #999999;
}
.page .record .list_item .right.data-v-e4f6867a {
  width: 92rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
}

