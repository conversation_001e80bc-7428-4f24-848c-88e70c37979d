<view class="custom-tabbar fixed flex-center bg-base border-top data-v-fb3b0a28"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="value"><view data-event-opts="{{[['tap',[['changeTab',['$0'],[[['tabbarConfig','value',item.$orig.value,'value']]]]]]]}}" class="flex-center flex-column data-v-fb3b0a28" style="{{'width:'+(100/$root.g0+'%')+';'+('color:'+(item.m0?activeColor:inactiveColor)+';')}}" catchtap="__e"><view class="icon-wrapper data-v-fb3b0a28"><u-icon vue-id="{{'6c2538cc-1-'+index}}" name="{{item.$orig.icon}}" color="{{item.m1?activeColor:inactiveColor}}" size="28" class="data-v-fb3b0a28" bind:__l="__l"></u-icon></view><view class="tab-text data-v-fb3b0a28">{{item.$orig.name}}</view></view></block></view>