<view class="page data-v-2b3ca7fc"><view class="tabs data-v-2b3ca7fc"><view data-event-opts="{{[['tap',[['switchTab',[1]]]]]}}" class="{{['tab_item','data-v-2b3ca7fc',(currentTab===1)?'active':'']}}" bindtap="__e">邀请的用户订单</view><view data-event-opts="{{[['tap',[['switchTab',[2]]]]]}}" class="{{['tab_item','data-v-2b3ca7fc',(currentTab===2)?'active':'']}}" bindtap="__e">邀请的师傅订单</view></view><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="order_item data-v-2b3ca7fc"><view class="top data-v-2b3ca7fc"><view class="top_left data-v-2b3ca7fc">{{item.createTime}}</view><block wx:if="{{item.payType==7}}"><view class="top_right data-v-2b3ca7fc">交易成功</view></block><block wx:if="{{item.payType==-1}}"><view class="top_right data-v-2b3ca7fc">已取消</view></block><block wx:if="{{item.payType==1}}"><view class="top_right data-v-2b3ca7fc">待支付</view></block><block wx:if="{{item.payType==3}}"><view class="top_right data-v-2b3ca7fc">待上门</view></block><block wx:if="{{item.payType==5}}"><view class="top_right data-v-2b3ca7fc">待服务</view></block><block wx:if="{{item.payType==6}}"><view class="top_right data-v-2b3ca7fc">服务中</view></block></view><view class="mid data-v-2b3ca7fc"><view class="mid_left data-v-2b3ca7fc">{{item.goodsName}}</view><block wx:if="{{currentTab===2}}"><view class="mid_right data-v-2b3ca7fc">{{"￥"+item.coachServicePrice}}</view></block><block wx:else><view class="mid_right data-v-2b3ca7fc">{{"￥"+item.payPrice}}</view></block></view><block wx:if="{{currentTab===1}}"><view class="bottom data-v-2b3ca7fc">{{"用户名："+(item.nickName?item.nickName:item.phone)}}</view></block><block wx:if="{{currentTab===2}}"><view class="bottom data-v-2b3ca7fc">{{"师傅名："+(item.nickName?item.nickName:item.phone)}}</view></block><view class="bottom data-v-2b3ca7fc">{{"订单号："+item.orderCode}}</view><view class="blue data-v-2b3ca7fc"></view></view></block><u-loadmore vue-id="3c58d88a-1" status="{{status}}" class="data-v-2b3ca7fc" bind:__l="__l"></u-loadmore></view>