<view class="page data-v-42e2fba5"><tabbar vue-id="197cfa01-1" cur="{{2}}" class="data-v-42e2fba5" bind:__l="__l"></tabbar><u-modal vue-id="197cfa01-2" show="{{show}}" title="删除商品" content="确认要删除该订单吗" showCancelButton="{{true}}" data-event-opts="{{[['^confirm',[['confirmDel']]],['^cancel',[['e0']]]]}}" bind:confirm="__e" bind:cancel="__e" class="data-v-42e2fba5" bind:__l="__l"></u-modal><block wx:if="{{configModalShow}}"><view data-event-opts="{{[['tap',[['closeConfigModal',['$event']]]]]}}" class="modal-overlay data-v-42e2fba5" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-container data-v-42e2fba5" catchtap="__e"><view class="modal-header data-v-42e2fba5"><text class="modal-title data-v-42e2fba5">修改信息</text><text data-event-opts="{{[['tap',[['closeConfigModal',['$event']]]]]}}" class="close-btn data-v-42e2fba5" bindtap="__e">×</text></view><block wx:if="{{modalData.loading}}"><view class="loading-container data-v-42e2fba5"><text class="data-v-42e2fba5">加载中...</text></view></block><block wx:else><scroll-view class="modal-scroll data-v-42e2fba5" scroll-y="true"><view class="config-content data-v-42e2fba5"><view class="card data-v-42e2fba5"><view class="bottom data-v-42e2fba5"><view class="left data-v-42e2fba5">已选：</view><view class="right data-v-42e2fba5"><view class="data-v-42e2fba5">{{''+modalData.yikoujiaprice+''}}</view><block wx:for="{{modalData.chooseArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tag data-v-42e2fba5">{{item.name}}</view></block><block wx:for="{{modalData.chosenInputValues}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="tag data-v-42e2fba5">{{''+item.problemDesc+": "+item.val+''}}</view></block></view></view></view><block wx:for="{{modalData.list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="chol data-v-42e2fba5"><view class="choose data-v-42e2fba5"><view class="title data-v-42e2fba5"><block wx:if="{{item.isRequired==1}}"><label class="_span data-v-42e2fba5">*</label></block>{{item.problemDesc}}</view><view class="desc data-v-42e2fba5">{{item.problemContent}}</view><view class="cho_box data-v-42e2fba5"><block wx:for="{{item.options}}" wx:for-item="newItem" wx:for-index="newIndex" wx:key="newIndex"><view data-event-opts="{{[['tap',[['chooseOne',[index,newIndex,'$0'],[[['modalData.list','',index,'inputType']]]]]]]}}" class="box_item data-v-42e2fba5" style="{{(newItem.choose?'border:2rpx solid #2E80FE;color: #2E80FE;':'')}}" bindtap="__e">{{''+newItem.name+''}}<view class="ok data-v-42e2fba5" style="{{(newItem.choose?'':'display:none;')}}"><uni-icons vue-id="{{'197cfa01-3-'+index+'-'+newIndex}}" type="checkmarkempty" size="8" color="#fff" class="data-v-42e2fba5" bind:__l="__l"></uni-icons></view></view></block></view></view><view class="fg data-v-42e2fba5"></view></view></block><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="__i0__" wx:key="id"><view class="chol data-v-42e2fba5"><view class="choose data-v-42e2fba5"><view class="title data-v-42e2fba5"><block wx:if="{{item.$orig.isRequired==1}}"><label class="_span data-v-42e2fba5">*</label></block>{{item.$orig.problemDesc}}</view><view class="desc data-v-42e2fba5">{{item.$orig.problemContent}}</view><view class="input-container data-v-42e2fba5"><input class="form-input data-v-42e2fba5" type="text" placeholder="{{'请输入'+item.$orig.problemDesc}}" data-event-opts="{{[['input',[['__set_model',['$0','val','$event',[]],['modalData.form.data.'+item.m1+'']]]]]}}" value="{{modalData.form.data[item.m2].val}}" bindinput="__e"/></view></view><view class="fg data-v-42e2fba5"></view></view></block><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="chol data-v-42e2fba5"><view class="choose data-v-42e2fba5"><view class="title data-v-42e2fba5"><block wx:if="{{item.$orig.isRequired==1}}"><label class="_span data-v-42e2fba5">*</label></block>{{item.$orig.problemDesc}}</view><view class="desc up data-v-42e2fba5">{{item.$orig.problemContent}}</view><upload vue-id="{{'197cfa01-4-'+index}}" imagelist="{{modalData.form.data[item.m3].val}}" imgtype="{{item.m4}}" text="上传图片" imgsize="{{3}}" data-event-opts="{{[['^upload',[['imgUpload']]],['^del',[['imgUpload']]]]}}" bind:upload="__e" bind:del="__e" class="data-v-42e2fba5" bind:__l="__l"></upload></view><view class="fg data-v-42e2fba5"></view></view></block></view></scroll-view></block><view class="modal-footer data-v-42e2fba5"><view data-event-opts="{{[['tap',[['closeConfigModal',['$event']]]]]}}" class="modal-btn cancel data-v-42e2fba5" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['submitConfig',['$event']]]]]}}" class="{{['modal-btn','confirm','data-v-42e2fba5',(modalData.isSubmitting)?'submitting':'']}}" bindtap="__e">{{''+(modalData.isSubmitting?'提交中...':'保存')+''}}</view></view></view></view></block><view class="data-v-42e2fba5"><block wx:for="{{$root.l3}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="car_item data-v-42e2fba5"><view class="trash data-v-42e2fba5"><label class="checkbox data-v-42e2fba5"><radio class="small-radio data-v-42e2fba5" checked="{{item.$orig.allChecked}}" color="#2979ff" data-event-opts="{{[['tap',[['toggleAll',['$0'],[[['list','',index]]]]]]]}}" bindtap="__e"></radio><text class="name data-v-42e2fba5" style="font-size:31rpx;font-weight:600;">{{item.$orig.name}}</text></label><u-icon vue-id="{{'197cfa01-5-'+index}}" name="trash" color="#2979ff" size="26" data-event-opts="{{[['^click',[['goTrash',['$0'],[[['list','',index]]]]]]]}}" bind:click="__e" class="data-v-42e2fba5" bind:__l="__l"></u-icon></view><view class="divider data-v-42e2fba5"></view><block wx:for="{{item.l2}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view class="top data-v-42e2fba5"><label class="checkbox data-v-42e2fba5"><radio class="small-radio data-v-42e2fba5" checked="{{item2.$orig.checked}}" color="#2979ff" data-event-opts="{{[['tap',[['toggleItem',['$0','$1'],[[['list','',index]],[['list','',index],['value','',index2]]]]]]]}}" bindtap="__e"></radio></label><image src="{{item2.$orig.cover}}" mode class="data-v-42e2fba5"></image><view data-event-opts="{{[['tap',[['showdetail',['$0'],[[['list','',index],['value','',index2]]]]]]]}}" class="right data-v-42e2fba5" bindtap="__e"><view class="choose _div data-v-42e2fba5">已选：<block wx:for="{{item2.$orig.settingVals}}" wx:for-item="item3" wx:for-index="index3" wx:key="index3"><label class="{{['_span','data-v-42e2fba5',(index3===item2.g0-1)?'last-item':'']}}">{{''+item3.val}}<block wx:if="{{index3!==item2.g1-1}}">,</block></label></block></view><view class="price data-v-42e2fba5"><text class="data-v-42e2fba5">师傅报价</text><u-number-box bind:input="__e" vue-id="{{'197cfa01-6-'+index+'-'+index2}}" min="{{1}}" value="{{item2.$orig.num}}" data-event-opts="{{[['^input',[['__set_model',['$0','num','$event',[]],[[['list','',index],['value','',index2]]]]]]]}}" class="data-v-42e2fba5" bind:__l="__l" vue-slots="{{['minus','plus']}}"><view slot="minus" class="data-v-42e2fba5"><view data-event-opts="{{[['tap',[['minus',['$0'],[[['list','',index],['value','',index2]]]]]]]}}" style="width:70rpx;height:60rpx;background-color:#ebecee;display:flex;justify-content:center;align-items:center;" bindtap="__e" class="data-v-42e2fba5"><u-icon vue-id="{{('197cfa01-7-'+index+'-'+index2)+','+('197cfa01-6-'+index+'-'+index2)}}" name="minus" color="#333" size="16" class="data-v-42e2fba5" bind:__l="__l"></u-icon></view></view><view slot="plus" class="data-v-42e2fba5"><view data-event-opts="{{[['tap',[['plus',['$0'],[[['list','',index],['value','',index2]]]]]]]}}" style="width:70rpx;height:60rpx;background-color:#ebecee;display:flex;justify-content:center;align-items:center;" bindtap="__e" class="data-v-42e2fba5"><u-icon vue-id="{{('197cfa01-8-'+index+'-'+index2)+','+('197cfa01-6-'+index+'-'+index2)}}" name="plus" color="#333" size="16" class="data-v-42e2fba5" bind:__l="__l"></u-icon></view></view></u-number-box></view></view></view></block></view></block></view><view class="footer data-v-42e2fba5"><view class="footer-left data-v-42e2fba5"><label class="checkbox data-v-42e2fba5"><radio class="large-radio data-v-42e2fba5" checked="{{isAllChecked}}" color="#2979ff" data-event-opts="{{[['tap',[['toggleAllCheck',['$event']]]]]}}" bindtap="__e"></radio><text class="data-v-42e2fba5">全选</text></label></view><view data-event-opts="{{[['tap',[['goToOrder',['$event']]]]]}}" class="footer-right data-v-42e2fba5" bindtap="__e">去下单</view></view></view>