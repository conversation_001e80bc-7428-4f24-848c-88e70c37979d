<view class="page data-v-5d289ec6"><tabbar vue-id="5c0ae409-1" cur="{{1}}" class="data-v-5d289ec6" bind:__l="__l"></tabbar><view class="header data-v-5d289ec6" id="header"><u-search vue-id="5c0ae409-2" placeholder="空调维修" showAction="{{false}}" inputAlign="center" value="{{keyword}}" data-event-opts="{{[['^focus',[['goUrl',['/user/search']]]],['^input',[['__set_model',['','keyword','$event',[]]]]]]}}" bind:focus="__e" bind:input="__e" class="data-v-5d289ec6" bind:__l="__l"></u-search></view><view class="main data-v-5d289ec6"><view class="left data-v-5d289ec6"><scroll-view class="scrollL data-v-5d289ec6" scroll-y="true"><block wx:if="{{loading}}"><view class="loading data-v-5d289ec6"><text class="data-v-5d289ec6">加载中...</text></view></block><block wx:else><block wx:if="{{error}}"><view class="error data-v-5d289ec6"><text class="data-v-5d289ec6">{{error}}</text></view></block><block wx:else><block wx:if="{{!$root.g0}}"><view class="no-content data-v-5d289ec6"><text class="data-v-5d289ec6">暂无分类数据</text></view></block><block wx:else><block wx:for="{{$root.l0}}" wx:for-item="category" wx:for-index="__i0__" wx:key="id"><view data-event-opts="{{[['tap',[['selectCategory',['$0'],[[['categories','id',category.$orig.id,'id']]]]]]]}}" class="left_item data-v-5d289ec6" bindtap="__e"><view class="{{['category_name','data-v-5d289ec6',(selectedCategoryId===category.$orig.id)?'active':'']}}">{{''+category.$orig.name+''}}</view><block wx:if="{{category.g1}}"><view class="sub_categories data-v-5d289ec6"><block wx:for="{{category.$orig.children}}" wx:for-item="subCategory" wx:for-index="__i1__" wx:key="id"><view data-event-opts="{{[['tap',[['selectSubCategory',['$0'],[[['categories','id',category.$orig.id],['children','id',subCategory.id,'id']]]]]]]}}" class="{{['sub_category','data-v-5d289ec6',(activeSubCategoryId===subCategory.id)?'active':'']}}" catchtap="__e">{{''+subCategory.name+''}}</view></block></view></block></view></block></block></block></block><view class="bottom_placeholder data-v-5d289ec6"></view></scroll-view></view><view class="right data-v-5d289ec6"><scroll-view class="scrollR data-v-5d289ec6" scroll-y="true" scroll-into-view="{{scrollToId}}" throttle="{{false}}" data-event-opts="{{[['scroll',[['onScroll',['$event']]]]]}}" bindscroll="__e"><block wx:if="{{loading}}"><view class="loading data-v-5d289ec6"><text class="data-v-5d289ec6">加载中...</text></view></block><block wx:else><block wx:if="{{error}}"><view class="error data-v-5d289ec6"><text class="data-v-5d289ec6">{{error}}</text></view></block><block wx:else><block wx:if="{{$root.g2}}"><view class="data-v-5d289ec6"><block wx:for="{{currentServices}}" wx:for-item="service" wx:for-index="__i2__" wx:key="id"><view class="right_box data-v-5d289ec6" id="{{'service-'+service.id}}"><view class="title data-v-5d289ec6">{{service.name}}</view><view class="img data-v-5d289ec6"><block wx:for="{{service.serviceList}}" wx:for-item="item" wx:for-index="__i3__" wx:key="id"><view data-event-opts="{{[['tap',[['goToDetails',['$0'],[[['currentServices','id',service.id],['serviceList','id',item.id,'id']]]]]]]}}" class="img_item data-v-5d289ec6" bindtap="__e"><image src="{{item.cover||'https://via.placeholder.com/144'}}" mode="aspectFill" class="data-v-5d289ec6"></image><view class="lname data-v-5d289ec6">{{item.title}}</view></view></block></view></view></block></view></block><block wx:else><view class="no-content data-v-5d289ec6"><text class="data-v-5d289ec6">暂无服务</text></view></block></block></block></scroll-view></view></view></view>