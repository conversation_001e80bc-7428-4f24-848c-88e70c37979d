<view class="{{[[(!imgclass||imgclass=='mini')?'flex-warp':''],[(imgclass&&imgclass!='mini')?'flex-center flex-column':'']]}}"><block wx:for="{{imagelist}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><view class="{{['rel','item-child',imgclass,[(imgsize>1)?'margin':'']]}}" style="{{(radius?'border-radius:50%;overflow:hidden;':'')}}"><block wx:if="{{filetype=='picture'}}"><image class="upload-img radius-16" mode="aspectFill" src="{{item.path}}" data-event-opts="{{[['tap',[['previewImage',['$0','$1'],[[['imagelist','',index]],'imagelist']]]]]}}" bindtap="__e"></image></block><block wx:if="{{filetype=='video'}}"><video class="upload-video rel radius-16" id="{{'video_'+index}}" loop="{{false}}" enable-play-gesture="{{true}}" enable-progress-gesture="{{true}}" show-center-play-btn="{{true}}" controls="{{true}}" src="{{item.path}}" data-id="{{item.id}}" objectFit="cover" data-index="{{index}}" data-event-opts="{{[['play',[['onPlay',['$event']]]],['pause',[['onPause',['$event']]]],['ended',[['onEnded',['$event']]]],['timeupdate',[['onTimeUpdate',['$event']]]],['waiting',[['onWaiting',['$event']]]],['progress',[['onProgress',['$event']]]],['loadedmetadata',[['onLoadedMetaData',['$event']]]]]}}" bindplay="__e" bindpause="__e" bindended="__e" bindtimeupdate="__e" bindwaiting="__e" bindprogress="__e" bindloadedmetadata="__e"><cover-view data-event-opts="{{[['tap',[['toDel',[index]]]]]}}" class="item-delete abs flex-center f-icontext c-base" style="{{'background:'+(primaryColor)+';'}}" bindtap="__e">删除</cover-view></video></block><block wx:if="{{filetype=='picture'}}"><block><block wx:if="{{imgsize>1}}"><view data-event-opts="{{[['tap',[['toDel',[index]]]]]}}" class="{{['guanbi','abs','flex-center',imgclass]}}" style="z-index:1;" bindtap="__e"><view class="iconfont icon-add rotate-45 c-base _i"></view></view></block><block wx:else><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="{{['flex-center','flex-column','item-child','upload-item','radius-16','abs',imgclass]}}" style="top:0;margin-top:0;background:rgba(0,0,0,0.5);" bindtap="__e"><view class="upload-icon flex-center c-title radius-10"><view class="iconfont icon-camera _i"></view></view><view class="f-caption c-base mt-sm">重新上传</view></view></block></block></block></view></block></block><block wx:if="{{$root.g0<imgsize}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="{{['radius-16','flex-center','flex-column','item-child','upload-item','fill-body',imgclass,[(imgsize>1)?'margin':'']]}}" bindtap="__e"><view class="upload-icon flex-center c-title radius-10"><view class="iconfont icon-camera _i"></view></view><block wx:if="{{text}}"><view class="f-caption c-caption mt-sm">{{text}}</view></block><block wx:if="{{imgsize>1}}"><view class="cur-imgsize f-caption c-caption">{{$root.g1+'/'+imgsize}}</view></block></view></block></view>