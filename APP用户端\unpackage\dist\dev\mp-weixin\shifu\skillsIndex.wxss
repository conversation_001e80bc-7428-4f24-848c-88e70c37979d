
.page.data-v-6c7d5886 {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* Reserve space for fixed footer */
  box-sizing: border-box; /* Ensures padding is included in height */
}
.main.data-v-6c7d5886 {
  flex: 1;
  display: flex;
  overflow: hidden; /* Important for scroll-views inside */
}
.left.data-v-6c7d5886 {
  width: 190rpx;
  background-color: #f8f8f8;
  flex-shrink: 0; /* Prevent it from shrinking */
}
.scrollL.data-v-6c7d5886 {
  height: 100%;
  overflow-y: auto;
}
.left_item.data-v-6c7d5886 {
  padding: 0 20rpx;
  min-height: 100rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  border-left: 6rpx solid transparent;
  transition: all 0.2s;
  display: flex;
  flex-direction: column;
  justify-content: center; /* Center content vertically */
}
.left_item.active.data-v-6c7d5886 {
  color: #2e80fe;
  font-size: 30rpx;
  border-left-color: #2e80fe;
  background-color: #fff;
}
.category_name.data-v-6c7d5886 {
  height: 100rpx; /* Ensure consistent height for names */
  width: 100%;
  display: flex;
  align-items: center;
}
.right.data-v-6c7d5886 {
  flex: 1;
  background-color: #fff;
  border-radius: 12rpx 12rpx 0 0;
  margin-left: 10rpx;
  overflow: hidden; /* Important for scroll-views inside */
}
.scrollR.data-v-6c7d5886 {
  height: 100%;
  overflow-y: auto;
}
.subcategory_section.data-v-6c7d5886 {
  margin-bottom: 15rpx;
}
.subcategory_header.data-v-6c7d5886 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fafafa;
  border-bottom: 1rpx solid #f0f0f0;
}
.subcategory_title.data-v-6c7d5886 {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  flex: 1; /* Allow title to take available space */
}
.selected_count.data-v-6c7d5886 {
  color: #2e80fe;
  font-weight: normal;
  margin-left: 10rpx; /* Add some spacing */
}
.select_all.data-v-6c7d5886 {
  font-size: 26rpx;
  color: #2e80fe;
  margin-left: 20rpx;
  flex-shrink: 0; /* Prevent it from shrinking */
}
.expand_icon.data-v-6c7d5886 {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx; /* Add some spacing */
  flex-shrink: 0; /* Prevent it from shrinking */
}
.service_items.data-v-6c7d5886 {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}
.service_item.data-v-6c7d5886 {
  width: calc(33.33% - 20rpx); /* Adjusted for margin */
  margin: 10rpx; /* Apply margin on all sides */
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s;
  text-align: center;
  padding: 0 10rpx;
  box-sizing: border-box; /* Include padding in width calculation */
  white-space: nowrap; /* Prevent text wrapping */
  overflow: hidden; /* Hide overflow text */
  text-overflow: ellipsis; /* Show ellipsis for overflow */
}
.service_item.active.data-v-6c7d5886 {
  background-color: #e6f0ff;
  color: #2e80fe;
  border: 1rpx solid #2e80fe;
}
.no-services.data-v-6c7d5886,
.no-content.data-v-6c7d5886,
.loading.data-v-6c7d5886,
.error.data-v-6c7d5886 {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx;
}
.error.data-v-6c7d5886 {
  color: #ff4d4f;
}
.footer.data-v-6c7d5886 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  padding: 15rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999; /* Increased z-index to ensure visibility */
  box-sizing: border-box; /* Include padding in height calculation */
}
.save_btn.data-v-6c7d5886 {
  width: 90%;
  height: 90rpx;
  background-color: #2e80fe;
  color: white;
  border-radius: 45rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.save_btn.disabled.data-v-6c7d5886 {
  background-color: #cccccc;
  cursor: not-allowed;
}

