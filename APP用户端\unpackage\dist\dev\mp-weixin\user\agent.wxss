@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-06dcff4f {
  padding: 0 30rpx;
}
.page .site_item.data-v-06dcff4f {
  margin-top: 40rpx;
  min-height: 180rpx;
  /* Increased to accommodate vertical icons */
  display: flex;
  align-items: flex-start;
}
.page .site_item .image-container.data-v-06dcff4f {
  width: 182rpx;
  height: 136rpx;
  margin-right: 20rpx;
  overflow: hidden;
  border-radius: 10rpx;
  position: relative;
}
.page .site_item .image-container image.data-v-06dcff4f {
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}
.page .site_item .content.data-v-06dcff4f {
  flex: 1;
  min-height: 100%;
  border-bottom: 2rpx solid #E9E9E9;
}
.page .site_item .content .name.data-v-06dcff4f {
  font-size: 28rpx;
  font-weight: 400;
  color: #333333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}
.page .site_item .content .address.data-v-06dcff4f {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 28rpx;
}
.page .site_item .content .address .position.data-v-06dcff4f {
  display: flex;
  align-items: center;
}
.page .site_item .content .address .position text.data-v-06dcff4f {
  font-size: 20rpx;
  font-weight: 400;
  color: #333333;
  white-space: normal;
}

