@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-a95b2d86 {
  padding-bottom: 200rpx;
}
.page.data-v-a95b2d86  .u-popup__content {
  display: none;
}
.page.data-v-a95b2d86  .u-number-box__plus {
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx !important;
  background-color: #fff !important;
  border: 1px solid #000;
}
.page.data-v-a95b2d86  .u-number-box__plus text {
  font-size: 24rpx !important;
  line-height: 36rpx !important;
}
.page.data-v-a95b2d86  .u-number-box__minus {
  border-radius: 50%;
  width: 36rpx;
  height: 36rpx !important;
  background-color: #fff !important;
  border: 1px solid #000;
}
.page.data-v-a95b2d86  .u-number-box__minus text {
  font-size: 24rpx !important;
  line-height: 36rpx !important;
}
.page.data-v-a95b2d86  .u-number-box__minus--disabled {
  border: 1px solid #ADADAD;
}
.page.data-v-a95b2d86  .u-number-box__input {
  background-color: #fff !important;
}
.page .service-divider.data-v-a95b2d86 {
  height: 2rpx;
  background-color: #EEEEEE;
  margin: 20rpx 0;
  width: 100%;
}
.page .choose_time.data-v-a95b2d86 {
  padding-top: 40rpx;
  width: 750rpx;
  height: 920rpx;
  background: #FFFFFF;
  border-radius: 40rpx 40rpx 0rpx 0rpx;
  opacity: 1;
  position: fixed;
  bottom: 0;
  z-index: 10088;
  transition: all 0.5s;
}
.page .choose_time .head.data-v-a95b2d86 {
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
  text-align: center;
}
.page .choose_time .close.data-v-a95b2d86 {
  position: absolute;
  top: 44rpx;
  right: 32rpx;
}
.page .choose_time .close image.data-v-a95b2d86 {
  width: 37rpx;
  height: 37rpx;
}
.page .choose_time .date.data-v-a95b2d86 {
  margin-top: 40rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.page .choose_time .date .date_item.data-v-a95b2d86 {
  text-align: center;
  font-size: 28rpx;
  font-weight: 400;
  color: #171717;
}
.page .choose_time .date .date_item .hk.data-v-a95b2d86 {
  margin-top: 8rpx;
  width: 100%;
  height: 6rpx;
  background: #2E80FE;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  opacity: 1;
}
.page .choose_time .time_all.data-v-a95b2d86 {
  margin-top: 10rpx;
  width: 750rpx;
  height: 520rpx;
  background: #F7F7F7;
  padding: 20rpx 10rpx;
}
.page .choose_time .time_all .time_columns.data-v-a95b2d86 {
  display: flex;
  justify-content: space-around;
}
.page .choose_time .time_all .time_columns .time_column.data-v-a95b2d86 {
  width: 330rpx;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}
.page .choose_time .time_all .time_columns .time_column .time_item.data-v-a95b2d86 {
  width: 100%;
  height: 80rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
  text-align: center;
  line-height: 80rpx;
}
.page .choose_time .btn.data-v-a95b2d86 {
  margin: 0 auto;
  margin-top: 28rpx;
  width: 686rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
  opacity: 1;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  text-align: center;
  line-height: 98rpx;
}
.page .footer.data-v-a95b2d86 {
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 202rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  background-color: #fff;
}
.page .footer .left.data-v-a95b2d86 {
  font-size: 40rpx;
  font-weight: 600;
  color: #E72427;
}
.page .footer .mid.data-v-a95b2d86 {
  width: -webkit-fit-content;
  width: fit-content;
  height: 98rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  color: #2E80FE;
  line-height: 98rpx;
  text-align: center;
  font-weight: 700;
  border: 2rpx solid #2E80FE;
  padding: 0 15rpx;
}
.page .footer .right.data-v-a95b2d86 {
  width: 294rpx;
  height: 98rpx;
  background: #2E80FE;
  border-radius: 50rpx 50rpx 50rpx 50rpx;
  opacity: 1;
  font-size: 32rpx;
  font-weight: 500;
  color: #FFFFFF;
  line-height: 98rpx;
  text-align: center;
}
.page .footer .disabled.data-v-a95b2d86 {
  opacity: 0.6;
  pointer-events: none;
}
.page .fg.data-v-a95b2d86 {
  height: 20rpx;
  background-color: #F3F4F5;
}
.page .address.data-v-a95b2d86 {
  height: 164rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
}
.page .address .left .top.data-v-a95b2d86 {
  display: flex;
  align-items: center;
}
.page .address .left .top image.data-v-a95b2d86 {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
}
.page .address .left .top text.data-v-a95b2d86 {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
  max-width: 400rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .address .left .bottom.data-v-a95b2d86 {
  font-size: 24rpx;
  font-weight: 400;
  color: #ADADAD;
  padding-left: 56rpx;
  margin-top: 12rpx;
}
.page .time.data-v-a95b2d86 {
  border-top: 2rpx solid #F0F0F0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 120rpx;
  padding: 0 32rpx;
}
.page .time .left.data-v-a95b2d86 {
  display: flex;
  align-items: center;
}
.page .time .left image.data-v-a95b2d86 {
  width: 36rpx;
  height: 36rpx;
  margin-right: 20rpx;
}
.page .time .left text.data-v-a95b2d86 {
  font-size: 28rpx;
  font-weight: 500;
  color: #2E80FE;
}
.page .main.data-v-a95b2d86 {
  padding: 20rpx 32rpx;
}
.page .main .service-divider.data-v-a95b2d86 {
  height: 2rpx;
  background-color: #EEEEEE;
  margin: 30rpx 0;
  width: 100%;
}
.page .main .group-header.data-v-a95b2d86 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
}
.page .main .group-title.data-v-a95b2d86 {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}
.page .main .urgent-option.data-v-a95b2d86 {
  display: flex;
  align-items: center;
}
.page .main .urgent-text.data-v-a95b2d86 {
  font-size: 24rpx;
  color: #333;
  margin-left: 4rpx;
}
.page .main .service-items.data-v-a95b2d86 {
  background-color: #FFFFFF;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0rpx 0rpx 8rpx 2rpx rgba(0, 0, 0, 0.05);
}
.page .main .main_item.data-v-a95b2d86 {
  display: flex;
  margin-bottom: 30rpx;
}
.page .main .main_item.data-v-a95b2d86:last-child {
  margin-bottom: 0;
}
.page .main .main_item image.data-v-a95b2d86 {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
}
.page .main .main_item .right.data-v-a95b2d86 {
  flex: 1;
}
.page .main .main_item .title.data-v-a95b2d86 {
  font-size: 28rpx;
  font-weight: 500;
  color: #171717;
  max-width: 450rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .main .main_item .selected-options.data-v-a95b2d86 {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #ADADAD;
  display: flex;
  flex-wrap: wrap;
}
.page .main .main_item .selected-label.data-v-a95b2d86 {
  margin-right: 4rpx;
}
.page .main .main_item .selected-value.data-v-a95b2d86 {
  margin-right: 4rpx;
}
.page .main .main_item .price.data-v-a95b2d86 {
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.page .main .main_item .price text.data-v-a95b2d86 {
  font-size: 28rpx;
  font-weight: 500;
  color: #2E80FE;
}
.page .notes.data-v-a95b2d86 {
  padding: 40rpx 32rpx;
}
.page .notes .title.data-v-a95b2d86 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}
.page .notes textarea.data-v-a95b2d86 {
  margin-top: 40rpx;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
  width: 686rpx;
  height: 242rpx;
  background: #F7F7F7;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  opacity: 1;
}
.group-header.data-v-a95b2d86 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.group-title.data-v-a95b2d86 {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}
.urgent-option.data-v-a95b2d86 {
  display: flex;
  align-items: center;
}
.urgent-text.data-v-a95b2d86 {
  font-size: 24rpx;
  color: #333;
  margin-left: 4rpx;
}

