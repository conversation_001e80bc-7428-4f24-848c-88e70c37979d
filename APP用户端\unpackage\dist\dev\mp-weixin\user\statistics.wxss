@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-19ccd549 {
  /* Tab styles */
}
.page .header.data-v-19ccd549 {
  padding: 40rpx 30rpx;
}
.page .header .head.data-v-19ccd549 {
  width: 690rpx;
  height: 186rpx;
  background: #2e80fe;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}
.page .header .head .left.data-v-19ccd549 {
  display: flex;
  align-items: center;
}
.page .header .head .left image.data-v-19ccd549 {
  width: 106rpx;
  height: 106rpx;
  border-radius: 50%;
}
.page .header .head .left .name.data-v-19ccd549 {
  margin-left: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
  max-width: 240rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .header .head .right.data-v-19ccd549 {
  width: -webkit-fit-content;
  width: fit-content;
  height: 74rpx;
  background: #81b3ff;
  border-radius: 12rpx;
  line-height: 74rpx;
  text-align: center;
  padding: 0 14rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ffffff;
}
.page .tabs.data-v-19ccd549 {
  display: flex;
  background: #fff;
  border-radius: 20rpx;
  margin: 0 30rpx 20rpx;
  padding: 10rpx;
}
.page .tabs .tab_item.data-v-19ccd549 {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 16rpx;
  transition: all 0.3s;
}
.page .tabs .tab_item.active.data-v-19ccd549 {
  background: #2E80FE;
  color: #fff;
  font-weight: 500;
}
.page .fg.data-v-19ccd549 {
  background: #f3f4f5;
  width: 100%;
  height: 20rpx;
}
.page .box.data-v-19ccd549 {
  padding: 40rpx 30rpx;
}
.page .box .title.data-v-19ccd549 {
  font-size: 32rpx;
  font-weight: 500;
  color: #171717;
}
.page .box .list.data-v-19ccd549 {
  margin-top: 42rpx;
}
.page .box .list .list_item.data-v-19ccd549 {
  display: flex;
  justify-content: space-between;
  /* Ensures space between main content and toggle */
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  /* Add some padding for better visual separation */
  border-bottom: 1rpx solid #eee;
  /* Add a subtle separator */
}
.page .box .list .list_item image.data-v-19ccd549 {
  width: 104rpx;
  height: 104rpx;
  border-radius: 50%;
}
.page .box .list .list_item .info.data-v-19ccd549 {
  margin-left: 20rpx;
}
.page .box .list .list_item .info .nam.data-v-19ccd549 {
  font-size: 28rpx;
  font-weight: 400;
  color: #171717;
  max-width: 480rpx;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.page .box .list .list_item .info .phone.data-v-19ccd549 {
  margin-top: 20rpx;
  font-size: 28rpx;
  font-weight: 400;
  color: #999999;
}
.page .box .list .expand-toggle.data-v-19ccd549 {
  font-size: 24rpx;
  color: #2e80fe;
  margin-top: 10rpx;
  cursor: pointer;
  text-align: center;
}
.page .box .list .children-list.data-v-19ccd549 {
  margin-left: 50rpx;
  /* Indent children list */
  border-left: 2rpx solid #eee;
  /* Visual indicator for nested list */
  padding-left: 20rpx;
  margin-top: 10rpx;
}
.page .box .list .children-list .child-item.data-v-19ccd549 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
  padding-bottom: 5rpx;
  border-bottom: 1rpx dotted #eee;
  /* Dotted line for child separators */
}
.page .box .list .children-list .child-item image.data-v-19ccd549 {
  width: 80rpx;
  /* Smaller image for children */
  height: 80rpx;
  border-radius: 50%;
}
.page .box .list .children-list .child-item .info.data-v-19ccd549 {
  margin-left: 15rpx;
}
.page .box .list .children-list .child-item .info .nam.data-v-19ccd549 {
  font-size: 26rpx;
}
.page .box .load-more.data-v-19ccd549 {
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: #2e80fe;
  cursor: pointer;
}

