@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
view.data-v-18418972, scroll-view.data-v-18418972, swiper-item.data-v-18418972 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-number-box.data-v-18418972 {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-number-box__slot.data-v-18418972 {
  touch-action: none;
}
.u-number-box__plus.data-v-18418972, .u-number-box__minus.data-v-18418972 {
  width: 35px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  touch-action: none;
}
.u-number-box__plus--hover.data-v-18418972, .u-number-box__minus--hover.data-v-18418972 {
  background-color: #E6E6E6 !important;
}
.u-number-box__plus--disabled.data-v-18418972, .u-number-box__minus--disabled.data-v-18418972 {
  color: #c8c9cc;
  background-color: #f7f8fa;
}
.u-number-box__plus.data-v-18418972 {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}
.u-number-box__minus.data-v-18418972 {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}
.u-number-box__input.data-v-18418972 {
  position: relative;
  text-align: center;
  font-size: 15px;
  padding: 0;
  margin: 0 2px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.u-number-box__input--disabled.data-v-18418972 {
  color: #c8c9cc;
  background-color: #f2f3f5;
}

