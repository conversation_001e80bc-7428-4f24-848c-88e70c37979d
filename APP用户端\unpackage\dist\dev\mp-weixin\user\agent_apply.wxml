<view class="page data-v-a02d1e04"><u-picker vue-id="4d452fc8-1" show="{{show}}" columns="{{columns}}" keyName="title" data-event-opts="{{[['^cancel',[['e0']]],['^confirm',[['confirmType']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-a02d1e04" bind:__l="__l"></u-picker><u-picker vue-id="4d452fc8-2" show="{{showCity}}" loading="{{loading}}" columns="{{columnsCity}}" keyName="title" data-ref="uPicker" data-event-opts="{{[['^change',[['changeHandler']]],['^cancel',[['e1']]],['^confirm',[['confirmCity']]]]}}" bind:change="__e" bind:cancel="__e" bind:confirm="__e" class="data-v-a02d1e04 vue-ref" bind:__l="__l"></u-picker><block wx:if="{{status!==''}}"><view class="header data-v-a02d1e04" style="{{('color:'+arr[status].color)}}">{{arr[status].text}}</view></block><view class="main data-v-a02d1e04"><view class="main_item data-v-a02d1e04"><view class="title data-v-a02d1e04"><label class="_span data-v-a02d1e04">*</label>法人姓名</view><input type="text" placeholder="请输入姓名" data-event-opts="{{[['input',[['__set_model',['$0','legalPersonName','$event',[]],['form']]]]]}}" value="{{form.legalPersonName}}" bindinput="__e" class="data-v-a02d1e04"/></view><view class="main_item data-v-a02d1e04"><view class="title data-v-a02d1e04"><label class="_span data-v-a02d1e04">*</label>法人身份证号</view><input type="text" placeholder="请输入身份证号" data-event-opts="{{[['input',[['__set_model',['$0','legalPersonIdCard','$event',[]],['form']]]]]}}" value="{{form.legalPersonIdCard}}" bindinput="__e" class="data-v-a02d1e04"/></view><view class="main_item data-v-a02d1e04"><view class="title data-v-a02d1e04"><label class="_span data-v-a02d1e04">*</label>联系电话</view><input type="text" placeholder="请输入联系电话" data-event-opts="{{[['input',[['__set_model',['$0','legalPersonTel','$event',[]],['form']]]]]}}" value="{{form.legalPersonTel}}" bindinput="__e" class="data-v-a02d1e04"/></view><view class="main_item data-v-a02d1e04"><view class="title data-v-a02d1e04"><label class="_span data-v-a02d1e04">*</label>选择省市区代理</view><input type="text" placeholder="请选择代理级别" disabled="{{true}}" data-event-opts="{{[['tap',[['e2',['$event']]]],['input',[['__set_model',['$0','typename','$event',[]],['form']]]]]}}" value="{{form.typename}}" bindtap="__e" bindinput="__e" class="data-v-a02d1e04"/></view><view class="main_item data-v-a02d1e04"><view class="title data-v-a02d1e04"><label class="_span data-v-a02d1e04">*</label>选择区域</view><input type="text" placeholder="请选择代理区域" disabled="{{true}}" data-event-opts="{{[['tap',[['e3',['$event']]]],['input',[['__set_model',['$0','city','$event',[]],['form']]]]]}}" value="{{form.city}}" bindtap="__e" bindinput="__e" class="data-v-a02d1e04"/></view><view class="main_item data-v-a02d1e04"><view class="title data-v-a02d1e04"><label class="_span data-v-a02d1e04">*</label>上传法人身份证照片</view><view class="card data-v-a02d1e04"><view class="card_item data-v-a02d1e04"><view class="top data-v-a02d1e04"><view class="das data-v-a02d1e04"><view class="up data-v-a02d1e04"><upload vue-id="4d452fc8-3" imagelist="{{form.legalPersonIdCardImg1}}" imgtype="legalPersonIdCardImg1" imgclass="id_card_box" text="身份证人像面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]]]}}" bind:upload="__e" class="data-v-a02d1e04" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-a02d1e04">拍摄人像面</view></view><view class="card_item data-v-a02d1e04"><view class="top data-v-a02d1e04"><view class="das data-v-a02d1e04"><view class="up data-v-a02d1e04"><upload vue-id="4d452fc8-4" imagelist="{{form.legalPersonIdCardImg2}}" imgtype="legalPersonIdCardImg2" imgclass="id_card_box" text="身份证国徽面" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]]]}}" bind:upload="__e" class="data-v-a02d1e04" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-a02d1e04">拍摄国徽面</view></view></view></view><view class="main_item data-v-a02d1e04"><view class="title data-v-a02d1e04"><label class="_span data-v-a02d1e04">*</label>上传营业执照照片</view><view class="big data-v-a02d1e04"><view class="top data-v-a02d1e04"><view class="das data-v-a02d1e04"><view class="up data-v-a02d1e04"><upload vue-id="4d452fc8-5" imagelist="{{form.legalPersonLicense}}" imgtype="legalPersonLicense" imgclass="id_yy_box" text="营业执照" imgsize="{{1}}" data-event-opts="{{[['^upload',[['imgUpload']]]]}}" bind:upload="__e" class="data-v-a02d1e04" bind:__l="__l"></upload></view></view></view><view class="bottom data-v-a02d1e04">拍摄营业执照</view></view></view></view><view class="footer data-v-a02d1e04"><block wx:if="{{status!==1&&status!==0}}"><view data-event-opts="{{[['tap',[['submit',['$event']]]]]}}" class="btn data-v-a02d1e04" bindtap="__e">立即提交</view></block></view></view>