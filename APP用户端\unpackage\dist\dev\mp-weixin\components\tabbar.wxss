@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.custom-tabbar.data-v-852a8b4e {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) / 2);
  background-color: #fff;
  z-index: 1000;
  border-top: 1px solid #eee;
}
.tab-text.data-v-852a8b4e {
  font-size: 22rpx;
  margin-top: 5rpx;
  line-height: 32rpx;
}
.flex-center.data-v-852a8b4e {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-column.data-v-852a8b4e {
  flex-direction: column;
}
.bg-base.data-v-852a8b4e {
  background-color: #fff;
}
.icon-wrapper.data-v-852a8b4e {
  position: relative;
}
.badge.data-v-852a8b4e {
  position: absolute;
  top: -5rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: #E41F19;
  color: #fff;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20rpx;
}

